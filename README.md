# 家政服务管理小程序

一个专业的家政服务管理平台，为用户提供便捷的家政服务预约和管理功能。

## 项目特色

✅ **完整的用户角色管理**：支持管理员和普通用户两种角色，权限分离
✅ **丰富的业务功能**：服务项目管理、服务人员管理、订单管理
✅ **美观的界面设计**：现代化UI设计，用户体验友好
✅ **完善的数据展示**：首页轮播图、数据统计、图表展示
✅ **强大的交互功能**：搜索筛选、状态管理、实时更新

## 功能模块

### 🏠 首页
- 轮播图展示
- 快捷入口导航
- 数据统计概览
- 热门服务推荐
- 最新订单展示

### 👤 用户管理
- 用户登录/注册
- 角色权限控制（管理员/普通用户）
- 个人信息管理
- 退出登录

### 🛠️ 服务管理
- 服务项目列表
- 服务详情查看
- 服务分类筛选
- 服务搜索功能
- 管理员：添加/编辑/删除服务

### 👥 人员管理
- 服务人员列表
- 人员详情查看
- 人员状态管理
- 技能标签展示
- 管理员：添加/编辑/删除人员

### 📋 订单管理
- 订单列表展示
- 订单状态筛选
- 订单详情查看
- 订单状态更新
- 管理员：创建/编辑订单、分配人员

### 🎛️ 管理后台
- 数据概览统计
- 快捷操作入口
- 最新订单监控
- 人员状态汇总
- 系统设置管理

## 演示账号

### 管理员账号
- 手机号：13800000000
- 密码：123456
- 权限：完整的管理功能

### 普通用户账号
- 手机号：13800000001
- 密码：123456
- 权限：基础的用户功能

## 技术栈

- **前端框架**：微信小程序原生开发
- **样式设计**：WXSS + 响应式布局
- **数据管理**：本地存储 + 模拟数据
- **云服务**：微信云开发（可选）

## 安装运行

1. 下载微信开发者工具
2. 导入项目到开发者工具
3. 配置小程序AppID（可使用测试号）
4. 编译运行

## 🎮 快速体验

### 演示账号
- **管理员**：13800000000 / 123456（勾选管理员登录）
- **普通用户**：13800000001 / 123456
- **其他账号**：任意11位手机号 / 123456

### 核心功能体验
1. **登录系统** → 使用演示账号登录
2. **首页浏览** → 查看轮播图、统计数据、热门服务
3. **服务管理** → 浏览服务列表、查看详情、预约服务
4. **订单管理** → 查看订单列表、订单详情、状态操作
5. **人员管理** → 查看服务人员、搜索筛选
6. **管理后台** → 管理员专用的数据监控和快捷操作

## ✅ 项目完成度

### 已实现功能
- ✅ 完整的用户登录系统（管理员/普通用户）
- ✅ 美观的首页展示（轮播图、统计、列表）
- ✅ 服务项目管理（列表、详情、搜索、筛选）
- ✅ 服务人员管理（列表、搜索、状态显示）
- ✅ 订单管理（列表、详情、状态操作）
- ✅ 管理后台（数据概览、快捷操作）
- ✅ 权限控制（不同角色不同功能）
- ✅ 数据统一管理（utils/data.js）
- ✅ 响应式界面设计
- ✅ 完善的交互反馈

### 占位功能（框架已搭建）
- 🔄 服务项目的添加/编辑页面
- 🔄 服务人员的添加/编辑页面
- 🔄 订单的创建/编辑页面
- 🔄 高级功能（支付、地图、评价等）

## 🗄️ 数据库功能

### 数据库架构
- ✅ **用户表 (users)** - 存储用户账号信息
- ✅ **服务表 (services)** - 存储服务项目信息
- ✅ **服务人员表 (workers)** - 存储服务人员信息
- ✅ **订单表 (orders)** - 存储订单信息

### 数据库特性
- ✅ **微信云开发集成** - 使用微信云数据库
- ✅ **自动降级机制** - 数据库失败时使用模拟数据
- ✅ **完整的CRUD操作** - 增删改查功能
- ✅ **一键数据库初始化** - 自动创建表和初始数据
- ✅ **双模式支持** - 数据库模式 + 模拟数据模式

### 快速配置数据库
1. **开通云开发**：在微信开发者工具中开通云开发服务
2. **配置环境ID**：在 `app.js` 中填入您的云环境ID
3. **初始化数据库**：登录管理员 → 我的 → 数据库初始化
4. **开始使用**：数据库初始化完成后即可使用完整功能

## 功能演示

### 首页展示
- 精美的轮播图
- 直观的数据统计
- 便捷的快捷入口
- 热门服务推荐

### 用户体验
- 流畅的页面切换
- 友好的交互反馈
- 清晰的信息层级
- 便捷的操作流程

### 管理功能
- 完整的CRUD操作
- 实时的状态更新
- 灵活的权限控制
- 丰富的数据展示

## 开发说明

本项目采用模块化开发，每个功能模块独立设计，便于维护和扩展。所有的数据交互都预留了云开发接口，可以方便地接入真实的后端服务。

---

**家政服务管理小程序** - 让家政服务更简单、更高效！


/**app.wxss**/
/* 全局样式 */
page {
  background-color: #F8F9FA;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 全局输入框样式优化 */
input, textarea {
  font-size: 32rpx !important;
  line-height: 1.4 !important;
}

/* 修复微信小程序输入框文字显示问题 */
.wx-input {
  line-height: inherit !important;
  height: auto !important;
}

.wx-textarea {
  line-height: inherit !important;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

button {
  background: initial;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #4A90E2;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  border: none;
}

.btn-primary:active {
  background-color: #357ABD;
}

.btn-secondary {
  background-color: #6C757D;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  border: none;
}

.btn-danger {
  background-color: #DC3545;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  border: none;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 列表项样式 */
.list-item {
  background-color: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #E9ECEF;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #CED4DA;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: white;
  box-sizing: border-box;
  line-height: 80rpx;
  display: flex;
  align-items: center;
}

.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #CED4DA;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: white;
  min-height: 120rpx;
}

/* 状态标签 */
.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-active {
  background-color: #28A745;
}

.status-inactive {
  background-color: #6C757D;
}

.status-pending {
  background-color: #FFC107;
  color: #212529;
}

.status-completed {
  background-color: #28A745;
}

.status-cancelled {
  background-color: #DC3545;
}

/* 文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-secondary {
  color: #6C757D;
}

.text-success {
  color: #28A745;
}

.text-danger {
  color: #DC3545;
}

.text-warning {
  color: #FFC107;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 间距工具类 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.mr-10 { margin-right: 10rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }
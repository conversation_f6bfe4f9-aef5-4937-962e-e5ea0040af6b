// 数据库管理工具
const db = wx.cloud.database();

const DatabaseManager = {
  // 数据库集合名称
  collections: {
    users: 'users',           // 用户表
    services: 'services',     // 服务表
    workers: 'workers',       // 服务人员表
    orders: 'orders'          // 订单表
  },

  // 初始化数据库
  async initDatabase() {
    try {
      console.log('开始初始化数据库...');

      // 优先尝试调用云函数
      try {
        console.log('尝试调用云函数初始化数据库...');
        const result = await wx.cloud.callFunction({
          name: 'initDatabase'
        });

        if (result.result && result.result.success) {
          console.log('云函数初始化成功:', result.result);
          return {
            success: true,
            message: '数据库初始化成功（云函数模式）',
            data: result.result.data
          };
        } else {
          throw new Error(result.result?.message || '云函数执行失败');
        }
      } catch (cloudError) {
        console.warn('云函数调用失败，尝试直接操作数据库:', cloudError);

        // 如果云函数调用失败，尝试直接操作数据库
        // 初始化用户数据
        await this.initUsers();

        // 初始化服务数据
        await this.initServices();

        // 初始化服务人员数据
        await this.initWorkers();

        // 初始化订单数据
        await this.initOrders();

        console.log('直接数据库操作初始化完成！');
        return {
          success: true,
          message: '数据库初始化成功（直接操作模式）',
          fallback: true
        };
      }
    } catch (error) {
      console.error('数据库初始化失败:', error);
      return { success: false, message: '数据库初始化失败', error };
    }
  },

  // 初始化用户数据
  async initUsers() {
    const users = [
      {
        phone: '13800000000',
        name: '管理员',
        password: '123456',
        isAdmin: true,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000001',
        name: '普通用户',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      }
    ];

    for (let user of users) {
      try {
        // 检查用户是否已存在
        const existUser = await db.collection(this.collections.users)
          .where({ phone: user.phone })
          .get();
        
        if (existUser.data.length === 0) {
          await db.collection(this.collections.users).add({
            data: user
          });
          console.log(`用户 ${user.name} 添加成功`);
        }
      } catch (error) {
        console.error(`添加用户 ${user.name} 失败:`, error);
      }
    }
  },

  // 初始化服务数据
  async initServices() {
    const services = [
      {
        name: '家庭保洁',
        description: '专业的家庭清洁服务，包括地面清洁、家具擦拭、厨房卫生间深度清洁等',
        price: 80,
        duration: 3,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['深度清洁', '专业工具', '环保清洁剂'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '月嫂服务',
        description: '专业月嫂提供产妇护理、新生儿护理、营养配餐等全方位服务',
        price: 200,
        duration: 24,
        category: 'childcare',
        image: '/images/default-goods-image.png',
        tags: ['专业护理', '24小时', '营养配餐'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '老人陪护',
        description: '为老年人提供日常生活照料、健康监护、心理陪伴等服务',
        price: 150,
        duration: 8,
        category: 'eldercare',
        image: '/images/default-goods-image.png',
        tags: ['贴心照料', '经验丰富', '健康监护'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '家电维修',
        description: '专业维修各类家用电器，包括空调、洗衣机、冰箱、电视等',
        price: 100,
        duration: 2,
        category: 'maintenance',
        image: '/images/default-goods-image.png',
        tags: ['专业技师', '原厂配件', '质保服务'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '钟点工服务',
        description: '灵活的钟点工服务，可根据您的需求安排工作时间和内容',
        price: 50,
        duration: 2,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['灵活安排', '多样服务', '按时计费'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '育儿嫂服务',
        description: '专业育儿嫂提供婴幼儿护理、早教启蒙、营养配餐等服务',
        price: 180,
        duration: 8,
        category: 'childcare',
        image: '/images/default-goods-image.png',
        tags: ['专业护理', '早教启蒙', '营养配餐'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      }
    ];

    for (let service of services) {
      try {
        // 检查服务是否已存在
        const existService = await db.collection(this.collections.services)
          .where({ name: service.name })
          .get();
        
        if (existService.data.length === 0) {
          await db.collection(this.collections.services).add({
            data: service
          });
          console.log(`服务 ${service.name} 添加成功`);
        }
      } catch (error) {
        console.error(`添加服务 ${service.name} 失败:`, error);
      }
    }
  },

  // 初始化服务人员数据
  async initWorkers() {
    const workers = [
      {
        name: '王阿姨',
        phone: '137****9876',
        age: 45,
        experience: 8,
        rating: 4.8,
        avatar: '/images/icons/avatar.png',
        skills: ['家庭保洁', '老人护理', '烹饪'],
        description: '有丰富的家政服务经验，工作认真负责，深受客户好评。',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      },
      {
        name: '刘阿姨',
        phone: '135****2468',
        age: 38,
        experience: 5,
        rating: 4.9,
        avatar: '/images/icons/avatar.png',
        skills: ['月嫂服务', '育儿护理', '营养配餐'],
        description: '专业月嫂，持有相关资格证书，服务细致周到。',
        status: 'busy',
        statusText: '服务中',
        createTime: new Date()
      },
      {
        name: '张师傅',
        phone: '138****1357',
        age: 52,
        experience: 15,
        rating: 4.7,
        avatar: '/images/icons/avatar.png',
        skills: ['家电维修', '水电维修', '家具安装'],
        description: '专业维修师傅，技术过硬，解决各种家庭维修问题。',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      },
      {
        name: '李阿姨',
        phone: '139****7531',
        age: 42,
        experience: 6,
        rating: 4.6,
        avatar: '/images/icons/avatar.png',
        skills: ['老人陪护', '康复护理', '心理疏导'],
        description: '专业护理人员，有爱心和耐心，擅长老人护理。',
        status: 'unavailable',
        statusText: '休假中',
        createTime: new Date()
      }
    ];

    for (let worker of workers) {
      try {
        // 检查服务人员是否已存在
        const existWorker = await db.collection(this.collections.workers)
          .where({ name: worker.name })
          .get();
        
        if (existWorker.data.length === 0) {
          await db.collection(this.collections.workers).add({
            data: worker
          });
          console.log(`服务人员 ${worker.name} 添加成功`);
        }
      } catch (error) {
        console.error(`添加服务人员 ${worker.name} 失败:`, error);
      }
    }
  },

  // 初始化订单数据
  async initOrders() {
    const orders = [
      {
        orderNumber: 'HZ202401150001',
        serviceName: '家庭保洁',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-16 09:00-12:00',
        address: '杭州市西湖区文三路123号',
        totalPrice: 240,
        duration: 3,
        status: 'pending',
        statusText: '待确认',
        customerName: '张女士',
        customerPhone: '138****5678',
        workerName: '',
        workerPhone: '',
        notes: '请提前电话联系，家里有小孩需要注意安全。',
        createTime: new Date('2024-01-15 10:30')
      },
      {
        orderNumber: 'HZ202401140002',
        serviceName: '月嫂服务',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-15 08:00-次日08:00',
        address: '杭州市拱墅区莫干山路456号',
        totalPrice: 4800,
        duration: 24,
        status: 'in_progress',
        statusText: '进行中',
        customerName: '李女士',
        customerPhone: '139****1234',
        workerName: '王阿姨',
        workerPhone: '137****9876',
        notes: '产妇刚生产，需要专业护理，请准备相关用品。',
        createTime: new Date('2024-01-14 14:20')
      },
      {
        orderNumber: 'HZ202401130003',
        serviceName: '老人陪护',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-14 08:00-18:00',
        address: '杭州市江干区钱江路789号',
        totalPrice: 1200,
        duration: 8,
        status: 'completed',
        statusText: '已完成',
        customerName: '陈先生',
        customerPhone: '136****5432',
        workerName: '刘阿姨',
        workerPhone: '135****2468',
        notes: '老人有高血压，需要定时测量血压，注意饮食。',
        createTime: new Date('2024-01-13 16:45')
      }
    ];

    for (let order of orders) {
      try {
        // 检查订单是否已存在
        const existOrder = await db.collection(this.collections.orders)
          .where({ orderNumber: order.orderNumber })
          .get();
        
        if (existOrder.data.length === 0) {
          await db.collection(this.collections.orders).add({
            data: order
          });
          console.log(`订单 ${order.orderNumber} 添加成功`);
        }
      } catch (error) {
        console.error(`添加订单 ${order.orderNumber} 失败:`, error);
      }
    }
  }
};

module.exports = DatabaseManager;

// 数据管理工具
const API = require('./api.js');

const DataManager = {
  // 初始化标志
  initialized: false,

  // 服务数据
  services: [
    {
      id: 1,
      name: '家庭保洁',
      description: '专业的家庭清洁服务，包括地面清洁、家具擦拭、厨房卫生间深度清洁等',
      price: 80,
      duration: 3,
      category: 'cleaning',
      image: '/images/4.png',
      tags: ['深度清洁', '专业工具', '环保清洁剂'],
      status: 'active',
      statusText: '可预约'
    },
    {
      id: 2,
      name: '月嫂服务',
      description: '专业月嫂提供产妇护理、新生儿护理、营养配餐等全方位服务',
      price: 200,
      duration: 24,
      category: 'childcare',
      image: '/images/5.png',
      tags: ['专业护理', '24小时', '营养配餐'],
      status: 'active',
      statusText: '可预约'
    },
    {
      id: 3,
      name: '老人陪护',
      description: '为老年人提供日常生活照料、健康监护、心理陪伴等服务',
      price: 150,
      duration: 8,
      category: 'eldercare',
      image: '/images/6.png',
      tags: ['贴心照料', '经验丰富', '健康监护'],
      status: 'active',
      statusText: '可预约'
    },
    {
      id: 4,
      name: '家电维修',
      description: '专业维修各类家用电器，包括空调、洗衣机、冰箱、电视等',
      price: 100,
      duration: 2,
      category: 'maintenance',
      image: '/images/7.png',
      tags: ['专业技师', '原厂配件', '质保服务'],
      status: 'active',
      statusText: '可预约'
    },
    {
      id: 5,
      name: '钟点工服务',
      description: '灵活的钟点工服务，可根据您的需求安排工作时间和内容',
      price: 50,
      duration: 2,
      category: 'cleaning',
      image: '/images/8.png',
      tags: ['灵活安排', '多样服务', '按时计费'],
      status: 'active',
      statusText: '可预约'
    },
    {
      id: 6,
      name: '育儿嫂服务',
      description: '专业育儿嫂提供婴幼儿护理、早教启蒙、营养配餐等服务',
      price: 180,
      duration: 8,
      category: 'childcare',
      image: '/images/9.png',
      tags: ['专业护理', '早教启蒙', '营养配餐'],
      status: 'active',
      statusText: '可预约'
    }
  ],

  // 服务人员数据
  workers: [
    {
      id: 1,
      name: '王阿姨',
      phone: '137****9876',
      age: 45,
      experience: 8,
      rating: 4.8,
      avatar: '/images/icons/avatar.png',
      skills: ['家庭保洁', '老人护理', '烹饪'],
      description: '有丰富的家政服务经验，工作认真负责，深受客户好评。',
      status: 'available',
      statusText: '空闲中',
      location: {
        latitude: 30.2741,
        longitude: 120.1551,
        address: '杭州市西湖区文三路123号'
      }
    },
    {
      id: 2,
      name: '刘阿姨',
      phone: '135****2468',
      age: 38,
      experience: 5,
      rating: 4.9,
      avatar: '/images/icons/avatar.png',
      skills: ['月嫂服务', '育儿护理', '营养配餐'],
      description: '专业月嫂，持有相关资格证书，服务细致周到。',
      status: 'busy',
      statusText: '服务中',
      location: {
        latitude: 30.2853,
        longitude: 120.1564,
        address: '杭州市拱墅区莫干山路456号'
      }
    },
    {
      id: 3,
      name: '张师傅',
      phone: '138****1357',
      age: 52,
      experience: 15,
      rating: 4.7,
      avatar: '/images/icons/avatar.png',
      skills: ['家电维修', '水电维修', '家具安装'],
      description: '专业维修师傅，技术过硬，解决各种家庭维修问题。',
      status: 'available',
      statusText: '空闲中',
      location: {
        latitude: 30.2965,
        longitude: 120.1614,
        address: '杭州市江干区钱江路789号'
      }
    },
    {
      id: 4,
      name: '李阿姨',
      phone: '139****7531',
      age: 42,
      experience: 6,
      rating: 4.6,
      avatar: '/images/icons/avatar.png',
      skills: ['老人陪护', '康复护理', '心理疏导'],
      description: '专业护理人员，有爱心和耐心，擅长老人护理。',
      status: 'unavailable',
      statusText: '休假中',
      location: {
        latitude: 30.2588,
        longitude: 120.1292,
        address: '杭州市滨江区江南大道321号'
      }
    }
  ],

  // 订单数据
  orders: [
    {
      id: 1,
      orderNumber: 'HZ202401150001',
      serviceName: '家庭保洁',
      serviceImage: '/images/4.png',
      serviceTime: '2024-01-16 09:00-12:00',
      address: '杭州市西湖区文三路123号',
      totalPrice: 240,
      duration: 3,
      status: 'pending',
      statusText: '待确认',
      createTime: '2024-01-15 10:30',
      customerName: '张女士',
      customerPhone: '138****5678',
      workerName: '',
      workerPhone: ''
    },
    {
      id: 2,
      orderNumber: 'HZ202401140002',
      serviceName: '月嫂服务',
      serviceImage: '/images/5.png',
      serviceTime: '2024-01-15 08:00-次日08:00',
      address: '杭州市拱墅区莫干山路456号',
      totalPrice: 4800,
      duration: 24,
      status: 'in_progress',
      statusText: '进行中',
      createTime: '2024-01-14 14:20',
      customerName: '李女士',
      customerPhone: '139****1234',
      workerName: '王阿姨',
      workerPhone: '137****9876'
    },
    {
      id: 3,
      orderNumber: 'HZ202401130003',
      serviceName: '老人陪护',
      serviceImage: '/images/6.png',
      serviceTime: '2024-01-14 08:00-18:00',
      address: '杭州市江干区钱江路789号',
      totalPrice: 1200,
      duration: 8,
      status: 'completed',
      statusText: '已完成',
      createTime: '2024-01-13 16:45',
      customerName: '陈先生',
      customerPhone: '136****5432',
      workerName: '刘阿姨',
      workerPhone: '135****2468'
    },
    {
      id: 4,
      orderNumber: 'HZ202401120004',
      serviceName: '家电维修',
      serviceImage: '/images/7.png',
      serviceTime: '2024-01-13 14:00-16:00',
      address: '杭州市滨江区江南大道321号',
      totalPrice: 200,
      duration: 2,
      status: 'completed',
      statusText: '已完成',
      createTime: '2024-01-12 10:15',
      customerName: '王先生',
      customerPhone: '135****9876',
      workerName: '张师傅',
      workerPhone: '138****1357'
    },
    {
      id: 5,
      orderNumber: 'HZ202401110005',
      serviceName: '钟点工服务',
      serviceImage: '/images/8.png',
      serviceTime: '2024-01-12 10:00-12:00',
      address: '杭州市余杭区文一西路654号',
      totalPrice: 100,
      duration: 2,
      status: 'pending',
      statusText: '待确认',
      createTime: '2024-01-11 16:30',
      customerName: '赵女士',
      customerPhone: '137****2468',
      workerName: '',
      workerPhone: ''
    },
    {
      id: 6,
      orderNumber: 'HZ202401100006',
      serviceName: '育儿嫂服务',
      serviceImage: '/images/9.png',
      serviceTime: '2024-01-11 08:00-18:00',
      address: '杭州市萧山区市心中路987号',
      totalPrice: 1440,
      duration: 10,
      status: 'in_progress',
      statusText: '进行中',
      createTime: '2024-01-10 09:45',
      customerName: '孙女士',
      customerPhone: '139****7531',
      workerName: '李阿姨',
      workerPhone: '139****7531'
    }
  ],

  // 获取服务列表（优先从数据库获取）
  async getServices() {
    try {
      const result = await API.service.getAll();
      if (result.success && result.data.length > 0) {
        // 转换数据库数据格式
        return result.data.map(item => ({
          id: item._id,
          name: item.name,
          description: item.description,
          price: item.price,
          duration: item.duration,
          category: item.category,
          image: item.image,
          tags: item.tags,
          status: item.status,
          statusText: item.statusText
        }));
      }
    } catch (error) {
      console.log('从数据库获取服务失败，使用模拟数据:', error);
    }
    // 数据库获取失败，返回模拟数据
    return this.services;
  },

  // 同步获取服务列表（模拟数据）
  getServicesSync() {
    return this.services;
  },

  // 根据ID获取服务
  getServiceById(id) {
    console.log('DataManager.getServiceById 查找ID:', id, '类型:', typeof id);
    console.log('可用的服务列表:', this.services.map(s => ({id: s.id, name: s.name})));

    if (!id) {
      console.log('ID为空，返回null');
      return null;
    }

    // 尝试不同的ID匹配方式
    let service = null;

    // 1. 直接匹配
    service = this.services.find(s => s.id === id);
    if (service) {
      console.log('直接匹配成功:', service);
      return service;
    }

    // 2. 数字匹配
    if (!isNaN(id)) {
      service = this.services.find(s => s.id === parseInt(id));
      if (service) {
        console.log('数字匹配成功:', service);
        return service;
      }
    }

    // 3. 字符串匹配
    service = this.services.find(s => s.id.toString() === id.toString());
    if (service) {
      console.log('字符串匹配成功:', service);
      return service;
    }

    // 4. 宽松匹配
    service = this.services.find(s => s.id == id);
    if (service) {
      console.log('宽松匹配成功:', service);
      return service;
    }

    // 5. 如果是数据库ID格式（长字符串），返回第一个服务作为默认
    if (typeof id === 'string' && id.length > 10) {
      console.log('检测到数据库ID格式，返回默认服务');
      return this.services[0] || null;
    }

    console.log('未找到匹配的服务');
    return null;
  },

  // 获取服务人员列表
  getWorkers() {
    return this.workers;
  },

  // 根据ID获取服务人员
  getWorkerById(id) {
    return this.workers.find(worker => worker.id === parseInt(id));
  },

  // 获取订单列表
  getOrders() {
    return this.orders;
  },

  // 根据ID获取订单
  getOrderById(id) {
    return this.orders.find(order => order.id === parseInt(id));
  },

  // 获取统计数据
  getStats() {
    return {
      totalServices: this.services.length,
      totalWorkers: this.workers.length,
      totalOrders: this.orders.length,
      completedOrders: this.orders.filter(order => order.status === 'completed').length,
      availableWorkers: this.workers.filter(worker => worker.status === 'available').length,
      busyWorkers: this.workers.filter(worker => worker.status === 'busy').length,
      unavailableWorkers: this.workers.filter(worker => worker.status === 'unavailable').length
    };
  },

  // 获取热门服务（前3个）
  getPopularServices() {
    return this.services.slice(0, 3);
  },

  // 获取最新订单（前3个）
  getRecentOrders() {
    return this.orders.slice(0, 3);
  },

  // 添加新订单
  addOrder(orderData) {
    console.log('DataManager.addOrder 接收到订单:', orderData);

    // 将新订单添加到数组开头（最新的在前面）
    this.orders.unshift(orderData);

    console.log('订单已添加，当前订单总数:', this.orders.length);
    console.log('最新的订单:', this.orders[0]);

    // 保存到本地存储
    try {
      wx.setStorageSync('orders', this.orders);
      console.log('订单已保存到本地存储');
    } catch (error) {
      console.error('保存到本地存储失败:', error);
    }

    return orderData;
  },

  // 获取所有订单
  getAllOrders() {
    return this.orders;
  },

  // 从本地存储加载订单
  loadOrdersFromStorage() {
    try {
      const storedOrders = wx.getStorageSync('orders');
      if (storedOrders && Array.isArray(storedOrders)) {
        // 合并存储的订单和默认订单，去重
        const allOrders = [...storedOrders, ...this.orders];
        const uniqueOrders = allOrders.filter((order, index, self) =>
          index === self.findIndex(o => o.orderNumber === order.orderNumber)
        );
        this.orders = uniqueOrders;
        console.log('从本地存储加载了订单，总数:', this.orders.length);
      }
    } catch (error) {
      console.error('从本地存储加载订单失败:', error);
    }
  },

  // 初始化DataManager
  init() {
    if (!this.initialized) {
      console.log('初始化DataManager...');
      this.loadOrdersFromStorage();
      this.initialized = true;
      console.log('DataManager初始化完成');
    }
  },

  // 为订单分配服务人员
  assignWorkerToOrder(orderId, worker) {
    console.log('DataManager.assignWorkerToOrder:', orderId, worker);

    // 查找订单
    const orderIndex = this.orders.findIndex(order =>
      order.id == orderId || order.orderNumber === orderId
    );

    if (orderIndex === -1) {
      console.error('未找到订单:', orderId);
      return false;
    }

    // 更新订单信息
    this.orders[orderIndex].workerName = worker.name;
    this.orders[orderIndex].workerPhone = worker.phone;
    this.orders[orderIndex].workerId = worker.id;
    this.orders[orderIndex].status = 'confirmed';
    this.orders[orderIndex].statusText = '已确认';

    console.log('订单已更新:', this.orders[orderIndex]);

    // 保存到本地存储
    try {
      wx.setStorageSync('orders', this.orders);
      console.log('订单更新已保存到本地存储');
    } catch (error) {
      console.error('保存订单更新失败:', error);
    }

    return true;
  },

  // 根据ID获取订单
  getOrderById(orderId) {
    return this.orders.find(order =>
      order.id == orderId || order.orderNumber === orderId
    );
  }
};

module.exports = DataManager;

// API 数据操作工具
const db = wx.cloud.database();

const API = {
  // 用户相关操作
  user: {
    // 用户登录
    async login(phone, password) {
      try {
        const result = await db.collection('users')
          .where({
            phone: phone,
            password: password
          })
          .get();
        
        if (result.data.length > 0) {
          const user = result.data[0];
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', {
            id: user._id,
            phone: user.phone,
            name: user.name,
            isAdmin: user.isAdmin,
            avatar: user.avatar
          });
          return { success: true, data: user };
        } else {
          return { success: false, message: '账号或密码错误' };
        }
      } catch (error) {
        console.error('登录失败:', error);
        return { success: false, message: '登录失败', error };
      }
    },

    // 用户注册
    async register(userData) {
      try {
        // 检查手机号是否已存在
        const existUser = await db.collection('users')
          .where({ phone: userData.phone })
          .get();
        
        if (existUser.data.length > 0) {
          return { success: false, message: '手机号已存在' };
        }

        const result = await db.collection('users').add({
          data: {
            ...userData,
            createTime: new Date()
          }
        });
        
        return { success: true, data: result };
      } catch (error) {
        console.error('注册失败:', error);
        return { success: false, message: '注册失败', error };
      }
    }
  },

  // 服务相关操作
  service: {
    // 获取所有服务
    async getAll() {
      try {
        const result = await db.collection('services')
          .orderBy('createTime', 'desc')
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取服务列表失败:', error);
        return { success: false, message: '获取服务列表失败', error };
      }
    },

    // 根据ID获取服务
    async getById(id) {
      try {
        const result = await db.collection('services').doc(id).get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取服务详情失败:', error);
        return { success: false, message: '获取服务详情失败', error };
      }
    },

    // 按分类获取服务
    async getByCategory(category) {
      try {
        const result = await db.collection('services')
          .where({ category: category })
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取分类服务失败:', error);
        return { success: false, message: '获取分类服务失败', error };
      }
    },

    // 搜索服务
    async search(keyword) {
      try {
        const result = await db.collection('services')
          .where({
            name: db.RegExp({
              regexp: keyword,
              options: 'i'
            })
          })
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('搜索服务失败:', error);
        return { success: false, message: '搜索服务失败', error };
      }
    },

    // 添加服务
    async add(serviceData) {
      try {
        const result = await db.collection('services').add({
          data: {
            ...serviceData,
            createTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('添加服务失败:', error);
        return { success: false, message: '添加服务失败', error };
      }
    },

    // 更新服务
    async update(id, serviceData) {
      try {
        const result = await db.collection('services').doc(id).update({
          data: {
            ...serviceData,
            updateTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('更新服务失败:', error);
        return { success: false, message: '更新服务失败', error };
      }
    },

    // 删除服务
    async delete(id) {
      try {
        const result = await db.collection('services').doc(id).remove();
        return { success: true, data: result };
      } catch (error) {
        console.error('删除服务失败:', error);
        return { success: false, message: '删除服务失败', error };
      }
    }
  },

  // 服务人员相关操作
  worker: {
    // 获取所有服务人员
    async getAll() {
      try {
        const result = await db.collection('workers')
          .orderBy('createTime', 'desc')
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取服务人员列表失败:', error);
        return { success: false, message: '获取服务人员列表失败', error };
      }
    },

    // 根据ID获取服务人员
    async getById(id) {
      try {
        const result = await db.collection('workers').doc(id).get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取服务人员详情失败:', error);
        return { success: false, message: '获取服务人员详情失败', error };
      }
    },

    // 按状态获取服务人员
    async getByStatus(status) {
      try {
        const result = await db.collection('workers')
          .where({ status: status })
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取服务人员失败:', error);
        return { success: false, message: '获取服务人员失败', error };
      }
    },

    // 搜索服务人员
    async search(keyword) {
      try {
        const result = await db.collection('workers')
          .where({
            name: db.RegExp({
              regexp: keyword,
              options: 'i'
            })
          })
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('搜索服务人员失败:', error);
        return { success: false, message: '搜索服务人员失败', error };
      }
    },

    // 添加服务人员
    async add(workerData) {
      try {
        const result = await db.collection('workers').add({
          data: {
            ...workerData,
            createTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('添加服务人员失败:', error);
        return { success: false, message: '添加服务人员失败', error };
      }
    },

    // 更新服务人员
    async update(id, workerData) {
      try {
        const result = await db.collection('workers').doc(id).update({
          data: {
            ...workerData,
            updateTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('更新服务人员失败:', error);
        return { success: false, message: '更新服务人员失败', error };
      }
    },

    // 删除服务人员
    async delete(id) {
      try {
        const result = await db.collection('workers').doc(id).remove();
        return { success: true, data: result };
      } catch (error) {
        console.error('删除服务人员失败:', error);
        return { success: false, message: '删除服务人员失败', error };
      }
    }
  },

  // 订单相关操作
  order: {
    // 获取所有订单
    async getAll() {
      try {
        const result = await db.collection('orders')
          .orderBy('createTime', 'desc')
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取订单列表失败:', error);
        return { success: false, message: '获取订单列表失败', error };
      }
    },

    // 根据ID获取订单
    async getById(id) {
      try {
        const result = await db.collection('orders').doc(id).get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取订单详情失败:', error);
        return { success: false, message: '获取订单详情失败', error };
      }
    },

    // 按状态获取订单
    async getByStatus(status) {
      try {
        const result = await db.collection('orders')
          .where({ status: status })
          .orderBy('createTime', 'desc')
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取订单失败:', error);
        return { success: false, message: '获取订单失败', error };
      }
    },

    // 按用户获取订单
    async getByUser(customerName) {
      try {
        const result = await db.collection('orders')
          .where({ customerName: customerName })
          .orderBy('createTime', 'desc')
          .get();
        return { success: true, data: result.data };
      } catch (error) {
        console.error('获取用户订单失败:', error);
        return { success: false, message: '获取用户订单失败', error };
      }
    },

    // 添加订单
    async add(orderData) {
      try {
        const result = await db.collection('orders').add({
          data: {
            ...orderData,
            createTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('添加订单失败:', error);
        return { success: false, message: '添加订单失败', error };
      }
    },

    // 更新订单
    async update(id, orderData) {
      try {
        const result = await db.collection('orders').doc(id).update({
          data: {
            ...orderData,
            updateTime: new Date()
          }
        });
        return { success: true, data: result };
      } catch (error) {
        console.error('更新订单失败:', error);
        return { success: false, message: '更新订单失败', error };
      }
    },

    // 删除订单
    async delete(id) {
      try {
        const result = await db.collection('orders').doc(id).remove();
        return { success: true, data: result };
      } catch (error) {
        console.error('删除订单失败:', error);
        return { success: false, message: '删除订单失败', error };
      }
    }
  },

  // 统计相关操作
  stats: {
    // 获取统计数据
    async getStats() {
      try {
        const [services, workers, orders] = await Promise.all([
          db.collection('services').count(),
          db.collection('workers').count(),
          db.collection('orders').count()
        ]);

        const completedOrders = await db.collection('orders')
          .where({ status: 'completed' })
          .count();

        const availableWorkers = await db.collection('workers')
          .where({ status: 'available' })
          .count();

        const busyWorkers = await db.collection('workers')
          .where({ status: 'busy' })
          .count();

        const unavailableWorkers = await db.collection('workers')
          .where({ status: 'unavailable' })
          .count();

        return {
          success: true,
          data: {
            totalServices: services.total,
            totalWorkers: workers.total,
            totalOrders: orders.total,
            completedOrders: completedOrders.total,
            availableWorkers: availableWorkers.total,
            busyWorkers: busyWorkers.total,
            unavailableWorkers: unavailableWorkers.total
          }
        };
      } catch (error) {
        console.error('获取统计数据失败:', error);
        return { success: false, message: '获取统计数据失败', error };
      }
    }
  }
};

module.exports = API;

// 数据库测试页面
Page({
  data: {
    logs: [],
    testing: false
  },

  onLoad() {
    this.addLog('数据库测试页面加载完成');
  },

  // 测试数据库连接
  async testConnection() {
    this.addLog('开始测试数据库连接...');
    
    try {
      const db = wx.cloud.database();
      
      // 尝试获取用户表数据
      const result = await db.collection('users').limit(1).get();
      
      this.addLog(`连接成功！用户表记录数: ${result.data.length}`);
      
      if (result.data.length > 0) {
        this.addLog(`第一条用户记录: ${JSON.stringify(result.data[0])}`);
      }
      
    } catch (error) {
      this.addLog(`连接失败: ${error.message}`);
    }
  },

  // 直接创建测试数据
  async createTestData() {
    if (this.data.testing) return;
    
    this.setData({ testing: true });
    this.addLog('开始创建测试数据...');
    
    try {
      const db = wx.cloud.database();
      
      // 创建一个测试用户
      const testUser = {
        phone: '13800000000',
        name: '管理员',
        password: '123456',
        isAdmin: true,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      };
      
      this.addLog('正在添加测试用户...');
      
      const addResult = await db.collection('users').add({
        data: testUser
      });
      
      this.addLog(`用户添加成功！ID: ${addResult._id}`);
      
      // 验证数据是否添加成功
      this.addLog('验证数据是否添加成功...');
      const checkResult = await db.collection('users').where({
        phone: '13800000000'
      }).get();
      
      this.addLog(`验证结果: 找到 ${checkResult.data.length} 条记录`);
      
      if (checkResult.data.length > 0) {
        this.addLog('数据添加验证成功！');
        wx.showToast({
          title: '测试成功',
          icon: 'success'
        });
      }
      
    } catch (error) {
      this.addLog(`创建失败: ${error.message}`);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    } finally {
      this.setData({ testing: false });
    }
  },

  // 批量创建所有数据
  async createAllData() {
    if (this.data.testing) return;
    
    this.setData({ testing: true });
    this.addLog('开始批量创建所有数据...');
    
    try {
      const db = wx.cloud.database();
      
      // 创建用户数据
      await this.createUsers(db);
      
      // 创建服务数据
      await this.createServices(db);
      
      // 创建服务人员数据
      await this.createWorkers(db);
      
      // 创建订单数据
      await this.createOrders(db);
      
      this.addLog('所有数据创建完成！');
      
      wx.showModal({
        title: '创建成功',
        content: '所有数据已创建完成！\n请到云开发控制台查看数据库表。',
        showCancel: false
      });
      
    } catch (error) {
      this.addLog(`批量创建失败: ${error.message}`);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    } finally {
      this.setData({ testing: false });
    }
  },

  // 创建用户数据
  async createUsers(db) {
    this.addLog('正在创建用户数据...');
    
    const users = [
      {
        phone: '13800000000',
        name: '管理员',
        password: '123456',
        isAdmin: true,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000001',
        name: '普通用户',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000002',
        name: '张女士',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      }
    ];

    for (let user of users) {
      try {
        // 检查是否已存在
        const exist = await db.collection('users').where({
          phone: user.phone
        }).get();
        
        if (exist.data.length === 0) {
          await db.collection('users').add({ data: user });
          this.addLog(`用户 ${user.name} 创建成功`);
        } else {
          this.addLog(`用户 ${user.name} 已存在`);
        }
      } catch (error) {
        this.addLog(`用户 ${user.name} 创建失败: ${error.message}`);
      }
    }
  },

  // 创建服务数据
  async createServices(db) {
    this.addLog('正在创建服务数据...');
    
    const services = [
      {
        name: '家庭保洁',
        description: '专业的家庭清洁服务',
        price: 80,
        duration: 3,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['深度清洁', '专业工具'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '月嫂服务',
        description: '专业月嫂提供产妇护理',
        price: 200,
        duration: 24,
        category: 'childcare',
        image: '/images/default-goods-image.png',
        tags: ['专业护理', '24小时'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      }
    ];

    for (let service of services) {
      try {
        const exist = await db.collection('services').where({
          name: service.name
        }).get();
        
        if (exist.data.length === 0) {
          await db.collection('services').add({ data: service });
          this.addLog(`服务 ${service.name} 创建成功`);
        } else {
          this.addLog(`服务 ${service.name} 已存在`);
        }
      } catch (error) {
        this.addLog(`服务 ${service.name} 创建失败: ${error.message}`);
      }
    }
  },

  // 创建服务人员数据
  async createWorkers(db) {
    this.addLog('正在创建服务人员数据...');
    
    const workers = [
      {
        name: '王阿姨',
        phone: '137****9876',
        age: 45,
        experience: 8,
        rating: 4.8,
        avatar: '/images/icons/avatar.png',
        skills: ['家庭保洁', '老人护理'],
        description: '有丰富的家政服务经验',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      }
    ];

    for (let worker of workers) {
      try {
        const exist = await db.collection('workers').where({
          name: worker.name
        }).get();
        
        if (exist.data.length === 0) {
          await db.collection('workers').add({ data: worker });
          this.addLog(`服务人员 ${worker.name} 创建成功`);
        } else {
          this.addLog(`服务人员 ${worker.name} 已存在`);
        }
      } catch (error) {
        this.addLog(`服务人员 ${worker.name} 创建失败: ${error.message}`);
      }
    }
  },

  // 创建订单数据
  async createOrders(db) {
    this.addLog('正在创建订单数据...');
    
    const orders = [
      {
        orderNumber: 'HZ202401150001',
        serviceName: '家庭保洁',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-16 09:00-12:00',
        address: '杭州市西湖区文三路123号',
        totalPrice: 240,
        duration: 3,
        status: 'pending',
        statusText: '待确认',
        customerName: '张女士',
        customerPhone: '138****5678',
        notes: '请提前电话联系',
        createTime: new Date()
      }
    ];

    for (let order of orders) {
      try {
        const exist = await db.collection('orders').where({
          orderNumber: order.orderNumber
        }).get();
        
        if (exist.data.length === 0) {
          await db.collection('orders').add({ data: order });
          this.addLog(`订单 ${order.orderNumber} 创建成功`);
        } else {
          this.addLog(`订单 ${order.orderNumber} 已存在`);
        }
      } catch (error) {
        this.addLog(`订单 ${order.orderNumber} 创建失败: ${error.message}`);
      }
    }
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] });
  },

  // 添加日志
  addLog(message) {
    const now = new Date();
    const time = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    const logs = this.data.logs;
    logs.push({
      time: time,
      message: message
    });
    
    this.setData({ logs: logs });
    console.log(`[${time}] ${message}`);
  }
});

<!--数据库测试页面-->
<view class="container">
  <view class="header">
    <text class="title">数据库测试工具</text>
    <text class="subtitle">用于测试和创建数据库数据</text>
  </view>

  <view class="actions">
    <button class="btn btn-primary" bindtap="testConnection">
      测试数据库连接
    </button>
    
    <button class="btn btn-secondary" bindtap="createTestData" disabled="{{testing}}">
      创建测试用户
    </button>
    
    <button class="btn btn-success" bindtap="createAllData" disabled="{{testing}}">
      {{testing ? '创建中...' : '批量创建所有数据'}}
    </button>
    
    <button class="btn btn-outline" bindtap="clearLogs">
      清空日志
    </button>
  </view>

  <view class="logs-section">
    <view class="logs-header">
      <text class="logs-title">操作日志</text>
      <text class="logs-count">{{logs.length}}条</text>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      
      <view class="empty-logs" wx:if="{{logs.length === 0}}">
        <text>暂无日志</text>
      </view>
    </scroll-view>
  </view>
</view>

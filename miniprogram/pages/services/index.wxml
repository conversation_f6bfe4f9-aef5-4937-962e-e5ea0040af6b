<!--services.wxml-->
<view class="container">
  <!-- 选择模式提示 -->
  <view class="select-mode-tip" wx:if="{{selectMode}}">
    <text class="tip-icon">👆</text>
    <text class="tip-text">点击选择您需要的服务项目</text>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/4.png"></image>
      <input class="search-input" placeholder="搜索服务项目" value="{{searchKeyword}}" bindinput="onSearchInput" />
    </view>
    <button class="search-btn btn-primary" bindtap="handleSearch">搜索</button>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-item {{selectedCategory === '' ? 'active' : ''}}" bindtap="selectCategory" data-category="">
        全部
      </view>
      <view class="category-item {{selectedCategory === item.id ? 'active' : ''}}" 
            wx:for="{{categories}}" wx:key="id" 
            bindtap="selectCategory" data-category="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>



  <!-- 服务列表 -->
  <view class="service-list">
    <view class="service-item card" wx:for="{{filteredServices}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <image class="service-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="service-content">
        <view class="service-header">
          <text class="service-name">{{item.name}}</text>
          <view class="service-status" catchtap="onStatusTap">
            <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
          </view>
        </view>
        
        <text class="service-desc">{{item.description}}</text>
        
        <view class="service-info">
          <text class="service-price">¥{{item.price}}/次</text>
          <text class="service-duration">{{item.duration}}小时</text>
        </view>
        
        <view class="service-tags">
          <text class="service-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>
        
        <!-- 管理员操作 -->
        <view class="admin-operations" wx:if="{{userInfo.isAdmin}}">
          <button class="btn-edit btn-secondary" bindtap="editService" data-id="{{item.id}}" catchtap="true">编辑</button>
          <button class="btn-delete btn-danger" bindtap="deleteService" data-id="{{item.id}}" catchtap="true">删除</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredServices.length === 0}}">
    <image class="empty-icon" src="/images/9.png"></image>
    <text class="empty-text">暂无服务项目</text>
  </view>
</view>

<!-- 浮动添加按钮（管理员） -->
<view class="fab-add" wx:if="{{userInfo.isAdmin}}" bindtap="addService">
  <text class="fab-icon">+</text>
</view>

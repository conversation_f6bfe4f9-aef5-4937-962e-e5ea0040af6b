const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    searchKeyword: '',
    selectedCategory: '',
    selectMode: false, // 是否为选择模式
    categories: [
      { id: 'cleaning', name: '保洁服务' },
      { id: 'childcare', name: '育儿服务' },
      { id: 'eldercare', name: '养老服务' },
      { id: 'maintenance', name: '维修服务' }
    ],
    services: [],
    filteredServices: []
  },

  onLoad(options) {
    console.log('服务页面 onLoad');
    this.checkUserLogin();
    this.loadServices();

    // 检查是否为选择模式
    if (options.selectMode === 'true') {
      this.setData({
        selectMode: true
      });
      wx.setNavigationBarTitle({
        title: '选择服务'
      });
    }
  },

  onShow() {
    console.log('服务页面 onShow');
    this.checkUserLogin();

    // 强制刷新服务数据
    setTimeout(() => {
      this.loadServices();
    }, 100);
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载服务列表
  async loadServices() {
    console.log('开始加载服务列表...');

    // 强制使用同步数据，确保图片路径正确
    const services = DataManager.getServicesSync();
    console.log('强制获取同步服务数据:', services);
    console.log('服务数据中的图片路径:', services.map(s => ({name: s.name, image: s.image})));

    this.setData({
      services: services,
      filteredServices: services
    });

    // 验证数据是否设置成功
    console.log('页面数据中的服务:', this.data.services);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 处理搜索
  handleSearch() {
    this.filterServices();
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.filterServices();
  },

  // 筛选服务
  filterServices() {
    const { services, searchKeyword, selectedCategory } = this.data;
    let filtered = services;

    // 按分类筛选
    if (selectedCategory) {
      filtered = filtered.filter(service => service.category === selectedCategory);
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase();
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(keyword) ||
        service.description.toLowerCase().includes(keyword) ||
        service.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    this.setData({
      filteredServices: filtered
    });
  },

  // 状态标签点击（阻止冒泡）
  onStatusTap(e) {
    // 阻止事件冒泡，不触发跳转到详情页
    console.log('状态标签被点击，已阻止跳转');
  },

  // 跳转到详情页或选择服务
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点击服务，ID:', id, '选择模式:', this.data.selectMode);

    if (this.data.selectMode) {
      // 选择模式：返回选中的服务
      const service = this.data.services.find(s => s.id === id);
      if (service) {
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];

        if (prevPage) {
          prevPage.setData({
            selectedService: service,
            totalCost: service.price
          });
          prevPage.checkCanSubmit();
        }

        wx.showToast({
          title: '服务已选择',
          icon: 'success',
          duration: 1500
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } else {
      // 正常模式：跳转到详情页
      wx.navigateTo({
        url: `/pages/service-detail/index?id=${id}`
      });
    }
  },

  // 添加服务
  addService() {
    wx.navigateTo({
      url: '/pages/service-manage/index'
    });
  },

  // 编辑服务
  editService(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-manage/index?id=${id}`
    });
  },

  // 删除服务
  deleteService(e) {
    const id = e.currentTarget.dataset.id;
    const service = this.data.services.find(s => s.id === id);

    wx.showModal({
      title: '确认删除',
      content: `确定要删除服务"${service.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数删除数据
          const services = this.data.services.filter(s => s.id !== id);
          this.setData({
            services: services
          });
          this.filterServices();

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  }
});

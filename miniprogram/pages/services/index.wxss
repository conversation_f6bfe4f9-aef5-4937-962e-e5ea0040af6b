/**services.wxss**/

.container {
  padding: 20rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 选择模式提示 */
.select-mode-tip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.tip-text {
  font-size: 28rpx;
  font-weight: bold;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 25rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

/* 分类筛选样式 */
.category-filter {
  margin-bottom: 20rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin-right: 20rpx;
  background-color: white;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid #E9ECEF;
}

.category-item.active {
  background-color: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* 管理员操作按钮 */
.admin-actions {
  margin-bottom: 20rpx;
  text-align: right;
}

.btn-add {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  padding: 30rpx;
}

.service-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.service-status {
  margin-left: 15rpx;
}

.status-tag {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  text-align: center;
  line-height: 1;
}

.status-active {
  background-color: #28A745;
}

.status-inactive {
  background-color: #6C757D;
}

.status-maintenance {
  background-color: #FFC107;
  color: #333;
}

.service-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.service-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.service-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-right: 20rpx;
}

.service-duration {
  font-size: 24rpx;
  color: #999;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}

.service-tag {
  background-color: #F8F9FA;
  color: #666;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

/* 管理员操作按钮 */
.admin-operations {
  display: flex;
  gap: 15rpx;
  margin-top: 15rpx;
  justify-content: flex-end;
}

.btn-edit,
.btn-delete {
  flex: 1;
  max-width: 120rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  color: white;
  text-align: center;
  line-height: 1;
}

.btn-edit {
  background-color: #6C757D;
}

.btn-delete {
  background-color: #DC3545;
}

.btn-edit:active {
  background-color: #5A6268;
}

.btn-delete:active {
  background-color: #C82333;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.btn-add-first {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 浮动添加按钮 */
.fab-add {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #4A90E2;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.3);
  z-index: 999;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

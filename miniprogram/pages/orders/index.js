const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    selectedStatus: '',
    orders: [],
    filteredOrders: []
  },

  onLoad() {
    this.checkUserLogin();
    this.loadOrders();
  },

  onShow() {
    this.checkUserLogin();
    this.loadOrders();
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载订单列表
  loadOrders() {
    // 确保DataManager已初始化
    DataManager.init();

    const orders = DataManager.getOrders();
    console.log('订单页面加载的订单数据:', orders);

    // 根据用户类型筛选订单
    let userOrders = orders;
    if (!this.data.userInfo.isAdmin) {
      // 普通用户只能看到自己的订单（这里简化处理，实际应该根据用户ID筛选）
      userOrders = orders.filter(order => order.customerName === '张女士'); // 模拟当前用户
    }

    this.setData({
      orders: userOrders,
      filteredOrders: userOrders
    });
  },

  // 选择状态筛选
  selectStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      selectedStatus: status
    });
    this.filterOrders();
  },

  // 筛选订单
  filterOrders() {
    const { orders, selectedStatus } = this.data;
    let filtered = orders;

    if (selectedStatus) {
      filtered = filtered.filter(order => order.status === selectedStatus);
    }

    this.setData({
      filteredOrders: filtered
    });
  },

  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/index?id=${id}`
    });
  },

  // 创建订单
  addOrder() {
    wx.navigateTo({
      url: '/pages/order-manage/index'
    });
  },

  // 编辑订单
  editOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-manage/index?id=${id}`
    });
  },

  // 取消订单
  cancelOrder(e) {
    const id = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === id);

    wx.showModal({
      title: '确认取消',
      content: `确定要取消订单"${order.orderNumber}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const orders = this.data.orders.map(o => {
            if (o.id === id) {
              return { ...o, status: 'cancelled', statusText: '已取消' };
            }
            return o;
          });

          this.setData({
            orders: orders
          });
          this.filterOrders();

          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
        }
      }
    });
  },

  // 确认完成
  confirmComplete(e) {
    const id = e.currentTarget.dataset.id;
    const order = this.data.orders.find(o => o.id === id);

    wx.showModal({
      title: '确认完成',
      content: `确认服务"${order.serviceName}"已完成？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const orders = this.data.orders.map(o => {
            if (o.id === id) {
              return { ...o, status: 'completed', statusText: '已完成' };
            }
            return o;
          });

          this.setData({
            orders: orders
          });
          this.filterOrders();

          wx.showToast({
            title: '订单已完成',
            icon: 'success'
          });
        }
      }
    });
  },

  // 分配服务人员
  assignWorker(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/workers/index?selectMode=true&orderId=${id}`
    });
  }
});

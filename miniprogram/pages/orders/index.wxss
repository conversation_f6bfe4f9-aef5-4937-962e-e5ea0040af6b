/**orders.wxss**/

.container {
  padding: 20rpx;
}

/* 状态筛选样式 */
.status-filter {
  margin-bottom: 20rpx;
}

.status-scroll {
  white-space: nowrap;
}

.status-item {
  display: inline-block;
  padding: 15rpx 25rpx;
  margin-right: 15rpx;
  background-color: white;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #666;
  border: 2rpx solid #E9ECEF;
}

.status-item.active {
  background-color: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* 管理员操作按钮 */
.admin-actions {
  margin-bottom: 20rpx;
  text-align: right;
}

.btn-add {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

/* 订单列表样式 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  padding: 30rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.order-info {
  flex: 1;
}

.order-number {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.order-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.order-status {
  margin-left: 15rpx;
}

.order-content {
  display: flex;
  margin-bottom: 20rpx;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-time,
.service-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.price-info {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.service-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-right: 20rpx;
}

.service-duration {
  font-size: 24rpx;
  color: #999;
}

/* 客户和服务人员信息 */
.customer-info,
.worker-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.customer-label,
.worker-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 15rpx;
}

.customer-name,
.worker-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

.customer-phone,
.worker-phone {
  font-size: 26rpx;
  color: #4A90E2;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #F0F0F0;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.order-actions button {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  flex: 1;
  max-width: 120rpx;
  min-width: 100rpx;
  text-align: center;
  line-height: 1;
}

.btn-detail {
  background-color: #6C757D;
  color: white;
}

.btn-cancel {
  background-color: #DC3545;
  color: white;
}

.btn-confirm {
  background-color: #28A745;
  color: white;
}

.btn-assign {
  background-color: #4A90E2;
  color: white;
}

.btn-edit {
  background-color: #6C757D;
  color: white;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.btn-add-first {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 浮动添加按钮 */
.fab-add {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #4A90E2;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.3);
  z-index: 999;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

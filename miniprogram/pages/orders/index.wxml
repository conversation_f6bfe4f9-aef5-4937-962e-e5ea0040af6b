<!--orders.wxml-->
<view class="container">
  <!-- 状态筛选 -->
  <view class="status-filter">
    <scroll-view class="status-scroll" scroll-x="true">
      <view class="status-item {{selectedStatus === '' ? 'active' : ''}}" bindtap="selectStatus" data-status="">
        全部
      </view>
      <view class="status-item {{selectedStatus === 'pending' ? 'active' : ''}}" bindtap="selectStatus" data-status="pending">
        待确认
      </view>
      <view class="status-item {{selectedStatus === 'confirmed' ? 'active' : ''}}" bindtap="selectStatus" data-status="confirmed">
        已确认
      </view>
      <view class="status-item {{selectedStatus === 'in_progress' ? 'active' : ''}}" bindtap="selectStatus" data-status="in_progress">
        进行中
      </view>
      <view class="status-item {{selectedStatus === 'completed' ? 'active' : ''}}" bindtap="selectStatus" data-status="completed">
        已完成
      </view>
      <view class="status-item {{selectedStatus === 'cancelled' ? 'active' : ''}}" bindtap="selectStatus" data-status="cancelled">
        已取消
      </view>
    </scroll-view>
  </view>



  <!-- 订单列表 -->
  <view class="order-list">
    <view class="order-item card" wx:for="{{filteredOrders}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">订单号：{{item.orderNumber}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status">
          <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
        </view>
      </view>

      <view class="order-content">
        <image class="service-image" src="{{item.serviceImage}}" mode="aspectFill"></image>
        <view class="service-info">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="service-time">服务时间：{{item.serviceTime}}</text>
          <text class="service-address">服务地址：{{item.address}}</text>
          <view class="price-info">
            <text class="service-price">¥{{item.totalPrice}}</text>
            <text class="service-duration">{{item.duration}}小时</text>
          </view>
        </view>
      </view>

      <view class="customer-info" wx:if="{{userInfo.isAdmin}}">
        <text class="customer-label">客户：</text>
        <text class="customer-name">{{item.customerName}}</text>
        <text class="customer-phone">{{item.customerPhone}}</text>
      </view>

      <view class="worker-info" wx:if="{{item.workerName}}">
        <text class="worker-label">服务人员：</text>
        <text class="worker-name">{{item.workerName}}</text>
        <text class="worker-phone">{{item.workerPhone}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions">
        <button class="btn-detail btn-secondary" bindtap="goToDetail" data-id="{{item.id}}" catchtap="true">查看详情</button>
        
        <!-- 用户操作 -->
        <block wx:if="{{!userInfo.isAdmin}}">
          <button class="btn-cancel btn-danger" wx:if="{{item.status === 'pending'}}" bindtap="cancelOrder" data-id="{{item.id}}" catchtap="true">取消订单</button>
          <button class="btn-confirm btn-primary" wx:if="{{item.status === 'in_progress'}}" bindtap="confirmComplete" data-id="{{item.id}}" catchtap="true">确认完成</button>
        </block>
        
        <!-- 管理员操作 -->
        <block wx:if="{{userInfo.isAdmin}}">
          <button class="btn-assign btn-primary" wx:if="{{item.status === 'pending'}}" bindtap="assignWorker" data-id="{{item.id}}" catchtap="true">分配人员</button>
          <button class="btn-edit btn-secondary" bindtap="editOrder" data-id="{{item.id}}" catchtap="true">编辑</button>
        </block>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredOrders.length === 0}}">
    <image class="empty-icon" src="/images/5.png"></image>
    <text class="empty-text">暂无订单</text>
  </view>
</view>

<!-- 浮动添加按钮（管理员） -->
<view class="fab-add" wx:if="{{userInfo.isAdmin}}" bindtap="addOrder">
  <text class="fab-icon">+</text>
</view>

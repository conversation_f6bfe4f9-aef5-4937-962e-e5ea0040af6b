<!--index.wxml-->
<view class="container">
  <!-- BGM控制器 -->
  <view class="bgm-controller {{bgmPlaying ? 'playing' : ''}}" bindtap="toggleBGM">
    <view class="bgm-icon">
      <text class="music-note">{{bgmPlaying ? '🎵' : '🔇'}}</text>
    </view>
    <view class="bgm-waves" wx:if="{{bgmPlaying}}">
      <view class="wave wave1"></view>
      <view class="wave wave2"></view>
      <view class="wave wave3"></view>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="banner-content">
          <text class="banner-title">{{item.title}}</text>
          <text class="banner-desc">{{item.desc}}</text>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快捷入口 -->
  <view class="quick-entry">
    <view class="entry-item" bindtap="goToServices">
      <image class="entry-icon" src="/images/icons/business.png"></image>
      <text class="entry-text">服务项目</text>
    </view>
    <view class="entry-item" bindtap="goToWorkers">
      <image class="entry-icon" src="/images/icons/avatar.png"></image>
      <text class="entry-text">服务人员</text>
    </view>
    <view class="entry-item" bindtap="goToOrders">
      <image class="entry-icon" src="/images/5.png"></image>
      <text class="entry-text">我的订单</text>
    </view>
    <view class="entry-item" wx:if="{{userInfo.isAdmin}}" bindtap="goToAdmin">
      <image class="entry-icon" src="/images/7.png"></image>
      <text class="entry-text">管理后台</text>
    </view>
    <view class="entry-item" bindtap="showNearbyServices">
      <image class="entry-icon" src="/images/6.png"></image>
      <text class="entry-text">附近服务</text>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="stats-title">数据统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{stats.totalServices}}</text>
        <text class="stats-label">服务项目</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{stats.totalWorkers}}</text>
        <text class="stats-label">服务人员</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{stats.totalOrders}}</text>
        <text class="stats-label">总订单</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{stats.completedOrders}}</text>
        <text class="stats-label">已完成</text>
      </view>
    </view>
  </view>

  <!-- 热门服务 -->
  <view class="popular-services">
    <view class="section-title">
      <text class="title-text">热门服务</text>
      <text class="more-text" bindtap="goToServices">查看更多 ></text>
    </view>
    <view class="service-list">
      <view class="service-item" wx:for="{{popularServices}}" wx:key="id" bindtap="goToServiceDetail" data-id="{{item.id}}">
        <image class="service-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="service-info">
          <text class="service-name">{{item.name}}</text>
          <text class="service-price">¥{{item.price}}/次</text>
          <view class="service-tags">
            <text class="service-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 最新订单 -->
  <view class="recent-orders" wx:if="{{recentOrders.length > 0}}">
    <view class="section-title">
      <text class="title-text">最新订单</text>
      <text class="more-text" bindtap="goToOrders">查看更多 ></text>
    </view>
    <view class="order-list">
      <view class="order-item" wx:for="{{recentOrders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.id}}">
        <view class="order-info">
          <text class="order-service">{{item.serviceName}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status">
          <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
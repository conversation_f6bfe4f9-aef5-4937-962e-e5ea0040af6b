const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    banners: [
      {
        id: 1,
        title: '专业家政服务',
        desc: '为您提供优质的家政服务体验',
        image: '/images/1.png'
      },
      {
        id: 2,
        title: '值得信赖的服务团队',
        desc: '经过专业培训的服务人员',
        image: '/images/2.png'
      },
      {
        id: 3,
        title: '便捷的预约系统',
        desc: '随时随地预约您需要的服务',
        image: '/images/3.png'
      }
    ],
    stats: {
      totalServices: 0,
      totalWorkers: 0,
      totalOrders: 0,
      completedOrders: 0
    },
    popularServices: [],
    recentOrders: [],
    bgmPlaying: false,
    bgmContext: null
  },

  onLoad() {
    this.checkUserLogin();
    this.loadData();
    this.initBGM();
  },

  onShow() {
    this.checkUserLogin();
    this.loadData();
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },

  // 加载页面数据
  loadData() {
    // 确保DataManager已初始化
    DataManager.init();

    this.loadStats();
    this.loadPopularServices();
    this.loadRecentOrders();
  },

  // 加载统计数据
  loadStats() {
    const stats = DataManager.getStats();
    this.setData({
      stats: stats
    });
  },

  // 加载热门服务
  async loadPopularServices() {
    console.log('开始加载热门服务...');

    // 强制使用同步数据，避免异步问题
    const services = DataManager.getServicesSync();
    console.log('强制获取同步服务数据:', services);

    if (services && services.length > 0) {
      const popularServices = services.slice(0, 4); // 取前4个作为热门服务
      console.log('热门服务数据:', popularServices);
      console.log('第一个服务的图片路径:', popularServices[0]?.image);

      this.setData({
        popularServices: popularServices
      });

      // 验证数据是否设置成功
      console.log('页面数据中的热门服务:', this.data.popularServices);
    } else {
      console.error('没有获取到服务数据');
    }
  },

  // 加载最新订单
  loadRecentOrders() {
    console.log('开始加载最新订单...');

    const orders = DataManager.getRecentOrders();
    console.log('获取到的订单数据:', orders);
    console.log('第一个订单的图片路径:', orders[0]?.serviceImage);

    this.setData({
      recentOrders: orders
    });

    // 验证数据是否设置成功
    console.log('页面数据中的最新订单:', this.data.recentOrders);
  },

  // 跳转到服务页面
  goToServices() {
    wx.switchTab({
      url: '/pages/services/index'
    });
  },

  // 跳转到服务人员页面
  goToWorkers() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '查看服务人员需要先登录账号',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/workers/index'
    });
  },

  // 跳转到订单页面
  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 跳转到管理后台
  goToAdmin() {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.isAdmin) {
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/admin/index'
    });
  },

  // 跳转到服务详情
  goToServiceDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/index?id=${id}`
    });
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/index?id=${id}`
    });
  },

  // 显示附近服务
  showNearbyServices() {
    // 首先检查位置权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户之前拒绝了位置权限
          wx.showModal({
            title: '需要位置权限',
            content: '为了为您推荐附近的家政服务，需要获取您的位置信息。请在设置中开启位置权限。',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 用户开启了权限，重新获取位置
                      this.getLocationAndShowServices();
                    }
                  }
                });
              } else {
                // 用户取消，显示默认服务列表
                this.showDefaultNearbyServices();
              }
            }
          });
        } else {
          // 权限正常，获取位置
          this.getLocationAndShowServices();
        }
      }
    });
  },

  // 获取位置并显示服务
  getLocationAndShowServices() {
    wx.showLoading({
      title: '获取位置中...'
    });

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        wx.hideLoading();
        console.log('获取位置成功:', res);

        // 模拟附近服务点
        const nearbyServices = [
          {
            name: '王阿姨家政服务',
            latitude: res.latitude + 0.001,
            longitude: res.longitude + 0.001,
            address: '距离您200米',
            phone: '13712349876'
          },
          {
            name: '专业保洁中心',
            latitude: res.latitude - 0.002,
            longitude: res.longitude + 0.002,
            address: '距离您500米',
            phone: '13812341234'
          },
          {
            name: '贴心月嫂服务',
            latitude: res.latitude + 0.003,
            longitude: res.longitude - 0.001,
            address: '距离您800米',
            phone: '13912345678'
          }
        ];

        // 显示附近服务列表
        this.showNearbyServicesList(nearbyServices);
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取位置失败:', error);

        // 显示默认服务列表
        this.showDefaultNearbyServices();
      }
    });
  },

  // 显示默认附近服务（无位置权限时）
  showDefaultNearbyServices() {
    const defaultServices = [
      {
        name: '王阿姨家政服务',
        latitude: 30.2741,
        longitude: 120.1551,
        address: '杭州市西湖区',
        phone: '13712349876'
      },
      {
        name: '专业保洁中心',
        latitude: 30.2853,
        longitude: 120.1564,
        address: '杭州市拱墅区',
        phone: '13812341234'
      },
      {
        name: '贴心月嫂服务',
        latitude: 30.2634,
        longitude: 120.1619,
        address: '杭州市江干区',
        phone: '13912345678'
      }
    ];

    wx.showToast({
      title: '显示默认服务',
      icon: 'none',
      duration: 1500
    });

    setTimeout(() => {
      this.showNearbyServicesList(defaultServices);
    }, 1500);
  },

  // 显示附近服务列表
  showNearbyServicesList(services) {
    const serviceNames = services.map(s => `${s.name} (${s.address})`);

    wx.showActionSheet({
      itemList: serviceNames,
      success: (res) => {
        const selectedService = services[res.tapIndex];

        wx.showModal({
          title: selectedService.name,
          content: `地址: ${selectedService.address}\n电话: ${selectedService.phone}`,
          confirmText: '拨打电话',
          cancelText: '查看位置',
          success: (modalRes) => {
            if (modalRes.confirm) {
              // 拨打电话
              wx.makePhoneCall({
                phoneNumber: selectedService.phone,
                fail: (error) => {
                  wx.showToast({
                    title: '拨打失败',
                    icon: 'none'
                  });
                }
              });
            } else if (modalRes.cancel) {
              // 查看位置
              wx.openLocation({
                latitude: selectedService.latitude,
                longitude: selectedService.longitude,
                name: selectedService.name,
                address: selectedService.address,
                scale: 15
              });
            }
          }
        });
      }
    });
  },

  // 初始化BGM
  initBGM() {
    const bgmContext = wx.createInnerAudioContext();

    // 使用网络音频资源（测试用）
    // 您也可以替换为本地音频文件：'/audio/bgm.mp3'
    bgmContext.src = 'https://www.bensound.com/bensound-music/bensound-ukulele.mp3'; // 网络音频资源
    bgmContext.loop = true; // 循环播放
    bgmContext.volume = 0.2; // 设置音量为20%

    // 音频事件监听
    bgmContext.onPlay(() => {
      console.log('BGM开始播放');
      this.setData({
        bgmPlaying: true
      });
    });

    bgmContext.onPause(() => {
      console.log('BGM暂停播放');
      this.setData({
        bgmPlaying: false
      });
    });

    bgmContext.onStop(() => {
      console.log('BGM停止播放');
      this.setData({
        bgmPlaying: false
      });
    });

    bgmContext.onError((error) => {
      console.error('BGM播放错误:', error);
      // 音频文件不存在时，使用模拟BGM效果
      this.setData({
        bgmContext: null // 清除音频上下文
      });

      wx.showToast({
        title: '使用模拟BGM效果',
        icon: 'none',
        duration: 2000
      });

      // 启用纯视觉BGM效果
      this.enableVisualBGM();
    });

    bgmContext.onCanplay(() => {
      console.log('BGM可以播放');
    });

    this.setData({
      bgmContext: bgmContext
    });

    // 检查用户之前的BGM设置
    const bgmEnabled = wx.getStorageSync('bgmEnabled');
    if (bgmEnabled !== false) { // 默认开启
      setTimeout(() => {
        this.playBGM();
      }, 1000); // 延迟1秒播放，避免页面加载时的卡顿
    }
  },

  // 播放BGM
  playBGM() {
    const { bgmContext } = this.data;
    if (bgmContext) {
      bgmContext.play();
      wx.setStorageSync('bgmEnabled', true);
    }
  },

  // 暂停BGM
  pauseBGM() {
    const { bgmContext } = this.data;
    if (bgmContext) {
      bgmContext.pause();
      wx.setStorageSync('bgmEnabled', false);
    }
  },

  // 切换BGM播放状态
  toggleBGM() {
    const { bgmPlaying } = this.data;

    if (bgmPlaying) {
      this.pauseBGM();
      wx.showToast({
        title: '背景音乐已关闭',
        icon: 'none',
        duration: 1500
      });
    } else {
      this.playBGM();
      wx.showToast({
        title: '背景音乐已开启',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 页面隐藏时暂停BGM
  onHide() {
    const { bgmContext, bgmPlaying } = this.data;
    if (bgmContext && bgmPlaying) {
      bgmContext.pause();
    }
  },

  // 页面显示时恢复BGM
  onShow() {
    this.checkUserLogin();

    // 强制刷新数据
    setTimeout(() => {
      this.loadData();
    }, 100);

    // 恢复BGM播放
    const bgmEnabled = wx.getStorageSync('bgmEnabled');
    if (bgmEnabled && this.data.bgmContext && !this.data.bgmPlaying) {
      setTimeout(() => {
        this.playBGM();
      }, 500);
    }
  },

  // 页面卸载时销毁BGM
  onUnload() {
    const { bgmContext } = this.data;
    if (bgmContext) {
      bgmContext.destroy();
    }

    // 清除视觉BGM定时器
    if (this.visualBGMTimer) {
      clearInterval(this.visualBGMTimer);
    }
  },

  // 启用纯视觉BGM效果（当音频文件不可用时）
  enableVisualBGM() {
    console.log('启用纯视觉BGM效果');

    // 检查用户设置
    const bgmEnabled = wx.getStorageSync('bgmEnabled');
    if (bgmEnabled !== false) {
      setTimeout(() => {
        this.startVisualBGM();
      }, 1000);
    }
  },

  // 开始视觉BGM效果
  startVisualBGM() {
    this.setData({
      bgmPlaying: true
    });

    wx.showToast({
      title: '视觉BGM已开启',
      icon: 'none',
      duration: 1500
    });
  },

  // 停止视觉BGM效果
  stopVisualBGM() {
    this.setData({
      bgmPlaying: false
    });

    wx.showToast({
      title: '视觉BGM已关闭',
      icon: 'none',
      duration: 1500
    });
  },

  // 修改切换BGM方法以支持纯视觉效果
  toggleBGM() {
    const { bgmPlaying, bgmContext } = this.data;

    if (bgmContext) {
      // 有音频文件时的正常逻辑
      if (bgmPlaying) {
        this.pauseBGM();
        wx.showToast({
          title: '背景音乐已关闭',
          icon: 'none',
          duration: 1500
        });
      } else {
        this.playBGM();
        wx.showToast({
          title: '背景音乐已开启',
          icon: 'none',
          duration: 1500
        });
      }
    } else {
      // 纯视觉效果时的逻辑
      if (bgmPlaying) {
        this.stopVisualBGM();
        wx.setStorageSync('bgmEnabled', false);
      } else {
        this.startVisualBGM();
        wx.setStorageSync('bgmEnabled', true);
      }
    }
  }
});

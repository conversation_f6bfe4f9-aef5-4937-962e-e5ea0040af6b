/**index.wxss**/

.container {
  padding: 0;
  background-color: #F8F9FA;
  position: relative;
}

/* 轮播图样式 */
.banner-section {
  margin-bottom: 20rpx;
}

.banner-swiper {
  height: 300rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 快捷入口样式 */
.quick-entry {
  display: flex;
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.entry-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
}

.entry-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.entry-text {
  font-size: 24rpx;
  color: #333;
}

/* 数据统计样式 */
.stats-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 10rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 区块标题样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-text {
  font-size: 28rpx;
  color: #4A90E2;
}

/* 热门服务样式 */
.popular-services {
  margin-bottom: 20rpx;
}

.service-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.service-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-price {
  font-size: 28rpx;
  color: #E74C3C;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-tag {
  background-color: #F8F9FA;
  color: #666;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

/* 最新订单样式 */
.recent-orders {
  margin-bottom: 20rpx;
}

.order-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-service {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.order-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.order-status {
  margin-left: 20rpx;
}

/* BGM控制器样式 */
.bgm-controller {
  position: fixed;
  top: 100rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
  z-index: 1000;
  transition: all 0.3s ease;
  animation: float 3s ease-in-out infinite;
}

.bgm-controller.playing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4rpx 20rpx rgba(240, 147, 251, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

.bgm-controller:active {
  transform: scale(0.95);
}

.bgm-icon {
  position: relative;
  z-index: 2;
}

.music-note {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 音波动画 */
.bgm-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: wave-expand 2s ease-out infinite;
}

.wave1 {
  animation-delay: 0s;
}

.wave2 {
  animation-delay: 0.7s;
}

.wave3 {
  animation-delay: 1.4s;
}

/* 动画定义 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes wave-expand {
  0% {
    width: 80rpx;
    height: 80rpx;
    opacity: 1;
  }
  100% {
    width: 160rpx;
    height: 160rpx;
    opacity: 0;
  }
}
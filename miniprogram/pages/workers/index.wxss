/**workers.wxss**/

.container {
  padding: 20rpx;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 25rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

/* 管理员操作按钮 */
.admin-actions {
  margin-bottom: 20rpx;
  text-align: right;
}

.btn-add {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

/* 服务人员列表样式 */
.worker-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.worker-item {
  display: flex;
  padding: 30rpx;
}

.worker-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

.worker-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.worker-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.worker-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.worker-status {
  margin-left: 15rpx;
}

.worker-phone {
  font-size: 26rpx;
  color: #4A90E2;
  margin-bottom: 15rpx;
}

.worker-info {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  gap: 15rpx;
  flex-wrap: wrap;
}

.worker-age,
.worker-experience,
.worker-rating {
  font-size: 24rpx;
  color: #666;
  padding: 4rpx 8rpx;
  background-color: #F8F9FA;
  border-radius: 4rpx;
}

.worker-rating {
  color: #E74C3C;
  font-weight: 500;
  background-color: #FFF5F5;
}

.worker-skills {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}

.skill-tag {
  background-color: #4A90E2;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

.worker-description {
  margin-bottom: 15rpx;
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 操作按钮 */
.worker-operations {
  display: flex;
  gap: 10rpx;
  margin-top: 15rpx;
  justify-content: flex-end;
}

.btn-edit,
.btn-delete,
.btn-select {
  flex: 1;
  min-width: 80rpx;
  max-width: 100rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  color: white;
  text-align: center;
  line-height: 1;
}

.btn-edit {
  background-color: #6C757D;
}

.btn-delete {
  background-color: #DC3545;
}

.btn-select {
  background-color: #28A745;
}

.btn-edit:active {
  background-color: #5A6268;
}

.btn-delete:active {
  background-color: #C82333;
}

.btn-select:active {
  background-color: #218838;
}

/* 位置信息样式 */
.worker-location {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.location-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  margin-top: 2rpx;
}

.location-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
  word-break: break-all;
}

.btn-map {
  width: 100%;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 6rpx;
  text-align: center;
}

.btn-map:active {
  background-color: #357ABD;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.btn-add-first {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

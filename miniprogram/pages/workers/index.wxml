<!--workers.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/8.png"></image>
      <input class="search-input" placeholder="搜索服务人员" value="{{searchKeyword}}" bindinput="onSearchInput" />
    </view>
    <button class="search-btn btn-primary" bindtap="handleSearch">搜索</button>
  </view>

  <!-- 管理员操作按钮 -->
  <view class="admin-actions" wx:if="{{userInfo.isAdmin}}">
    <button class="btn-add btn-primary" bindtap="addWorker">
      <image class="btn-icon" src="/images/9.png"></image>
      添加人员
    </button>
  </view>

  <!-- 服务人员列表 -->
  <view class="worker-list">
    <view class="worker-item card" wx:for="{{filteredWorkers}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <image class="worker-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
      <view class="worker-content">
        <view class="worker-header">
          <text class="worker-name">{{item.name}}</text>
          <view class="worker-status">
            <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
          </view>
        </view>
        
        <text class="worker-phone">{{item.phone}}</text>
        
        <view class="worker-info">
          <text class="worker-age">{{item.age}}岁</text>
          <text class="worker-experience">{{item.experience}}年经验</text>
          <text class="worker-rating">评分：{{item.rating}}</text>
        </view>
        
        <view class="worker-skills">
          <text class="skill-tag" wx:for="{{item.skills}}" wx:key="*this">{{item}}</text>
        </view>
        
        <view class="worker-description">
          <text class="description-text">{{item.description}}</text>
        </view>

        <!-- 位置信息 -->
        <view class="worker-location" wx:if="{{item.location}}">
          <view class="location-info">
            <text class="location-icon">📍</text>
            <text class="location-text">{{item.location.address}}</text>
          </view>
          <button class="btn-map" bindtap="showWorkerLocation" data-worker="{{item}}" catchtap="true">查看位置</button>
        </view>

        <!-- 操作按钮 -->
        <view class="worker-operations">
          <!-- 管理员操作 -->
          <block wx:if="{{userInfo.isAdmin && !selectMode}}">
            <button class="btn-edit btn-secondary" bindtap="editWorker" data-id="{{item.id}}" catchtap="true">编辑</button>
            <button class="btn-delete btn-danger" bindtap="deleteWorker" data-id="{{item.id}}" catchtap="true">删除</button>
          </block>

          <!-- 选择模式按钮 -->
          <block wx:if="{{selectMode}}">
            <button class="btn-select btn-primary" bindtap="selectWorker" data-id="{{item.id}}" catchtap="true">选择</button>
          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredWorkers.length === 0}}">
    <image class="empty-icon" src="/images/8.png"></image>
    <text class="empty-text">暂无服务人员</text>
    <button class="btn-add-first btn-primary" wx:if="{{userInfo.isAdmin}}" bindtap="addWorker">添加第一个服务人员</button>
  </view>
</view>

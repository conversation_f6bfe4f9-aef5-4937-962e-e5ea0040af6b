const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    searchKeyword: '',
    selectMode: false,
    orderId: null,
    workers: [],
    filteredWorkers: []
  },

  onLoad(options) {
    this.checkUserLogin();

    // 检查是否为选择模式
    if (options.selectMode) {
      this.setData({
        selectMode: true,
        orderId: options.orderId
      });
      wx.setNavigationBarTitle({
        title: '选择服务人员'
      });
    }

    this.loadWorkers();
  },

  onShow() {
    this.checkUserLogin();
    this.loadWorkers();
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载服务人员列表
  loadWorkers() {
    const workers = DataManager.getWorkers();
    this.setData({
      workers: workers,
      filteredWorkers: workers
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 处理搜索
  handleSearch() {
    this.filterWorkers();
  },

  // 筛选服务人员
  filterWorkers() {
    const { workers, searchKeyword } = this.data;
    let filtered = workers;

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase();
      filtered = filtered.filter(worker =>
        worker.name.toLowerCase().includes(keyword) ||
        worker.phone.includes(keyword) ||
        worker.skills.some(skill => skill.toLowerCase().includes(keyword)) ||
        worker.description.toLowerCase().includes(keyword)
      );
    }

    this.setData({
      filteredWorkers: filtered
    });
  },

  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/worker-detail/index?id=${id}`
    });
  },

  // 添加服务人员
  addWorker() {
    wx.navigateTo({
      url: '/pages/worker-manage/index'
    });
  },

  // 编辑服务人员
  editWorker(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/worker-manage/index?id=${id}`
    });
  },

  // 删除服务人员
  deleteWorker(e) {
    const id = e.currentTarget.dataset.id;
    const worker = this.data.workers.find(w => w.id === id);

    wx.showModal({
      title: '确认删除',
      content: `确定要删除服务人员"${worker.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数删除数据
          const workers = this.data.workers.filter(w => w.id !== id);
          this.setData({
            workers: workers
          });
          this.filterWorkers();

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 选择服务人员
  selectWorker(e) {
    const id = e.currentTarget.dataset.id;
    const worker = this.data.workers.find(w => w.id === id);
    const { orderId } = this.data;

    console.log('选择服务人员:', worker);
    console.log('订单ID:', orderId);

    if (worker.status !== 'available') {
      wx.showToast({
        title: '该服务人员当前不可用',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认选择',
      content: `确定选择"${worker.name}"为此订单的服务人员吗？`,
      success: (res) => {
        if (res.confirm) {
          try {
            // 更新订单信息
            const success = DataManager.assignWorkerToOrder(orderId, worker);

            if (success) {
              wx.showToast({
                title: '分配成功',
                icon: 'success'
              });

              setTimeout(() => {
                // 返回订单详情页并刷新数据
                wx.navigateBack({
                  success: () => {
                    const pages = getCurrentPages();
                    if (pages.length > 0) {
                      const prevPage = pages[pages.length - 1];
                      if (prevPage.loadOrderDetail) {
                        prevPage.loadOrderDetail();
                      }
                    }
                  }
                });
              }, 1500);
            } else {
              wx.showToast({
                title: '分配失败，请重试',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('分配服务人员失败:', error);
            wx.showToast({
              title: '分配失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 显示服务人员位置
  showWorkerLocation(e) {
    const worker = e.currentTarget.dataset.worker;

    if (!worker.location) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      });
      return;
    }

    // 使用微信内置地图查看位置
    wx.openLocation({
      latitude: worker.location.latitude,
      longitude: worker.location.longitude,
      name: worker.name,
      address: worker.location.address,
      scale: 15,
      success: () => {
        console.log('打开地图成功');
      },
      fail: (error) => {
        console.error('打开地图失败:', error);
        wx.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    });
  }
});

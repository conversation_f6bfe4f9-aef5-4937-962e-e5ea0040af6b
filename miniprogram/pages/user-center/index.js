Page({
  data: {
    userInfo: null,
    orderCount: 0
  },

  onLoad() {
    this.checkUserLogin();
  },

  onShow() {
    this.checkUserLogin();
    this.loadUserData();
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载用户数据
  loadUserData() {
    if (!this.data.userInfo) return;

    // 加载用户订单数量等数据
    this.loadOrderCount();
  },

  // 加载订单数量
  loadOrderCount() {
    // 模拟数据，实际应该从云数据库获取
    this.setData({
      orderCount: 3
    });
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    });
  },

  // 跳转到服务管理
  goToServiceManage() {
    wx.navigateTo({
      url: '/pages/service-manage/index'
    });
  },

  // 跳转到人员管理
  goToWorkerManage() {
    wx.navigateTo({
      url: '/pages/workers/index'
    });
  },

  // 跳转到订单管理
  goToOrderManage() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 跳转到数据统计
  goToDataStats() {
    wx.navigateTo({
      url: '/pages/admin/index'
    });
  },



  // 跳转到我的订单
  goToMyOrders() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },



  // 跳转到我的收藏
  goToFavorites() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到地址管理
  goToAddressManage() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到系统设置
  goToSettings() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 跳转到关于我们
  goToAbout() {
    wx.showModal({
      title: '关于我们',
      content: '家政服务管理小程序\n版本：1.0.0\n\n这是一个专业的家政服务管理平台，为用户提供便捷的家政服务预约和管理功能。',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户信息
          wx.removeStorageSync('userInfo');

          // 更新页面状态
          this.setData({
            userInfo: null,
            orderCount: 0
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          // 跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      }
    });
  }
});

<!--user-center.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-header">
    <view class="user-info" wx:if="{{userInfo}}">
      <image class="user-avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.name}}</text>
        <text class="user-phone">{{userInfo.phone}}</text>
        <view class="user-role">
          <view class="role-tag {{userInfo.isAdmin ? 'admin' : 'user'}}">
            <text class="role-icon">{{userInfo.isAdmin ? '👑' : '👤'}}</text>
            <text class="role-text">{{userInfo.isAdmin ? '管理员' : '普通用户'}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="login-prompt" wx:else>
      <image class="default-avatar" src="/images/8.png"></image>
      <text class="login-text">请先登录</text>
      <button class="btn-login btn-primary" bindtap="goToLogin">立即登录</button>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section" wx:if="{{userInfo}}">
    <!-- 管理员菜单 -->
    <view class="menu-group" wx:if="{{userInfo.isAdmin}}">
      <view class="menu-title">管理功能</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="goToServiceManage">
          <image class="menu-icon" src="/images/4.png"></image>
          <text class="menu-text">服务管理</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToWorkerManage">
          <image class="menu-icon" src="/images/8.png"></image>
          <text class="menu-text">人员管理</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToOrderManage">
          <image class="menu-icon" src="/images/5.png"></image>
          <text class="menu-text">订单管理</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToDataStats">
          <image class="menu-icon" src="/images/7.png"></image>
          <text class="menu-text">数据统计</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>


      </view>
    </view>

    <!-- 通用菜单 -->
    <view class="menu-group">
      <view class="menu-title">我的服务</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="goToMyOrders">
          <image class="menu-icon" src="/images/5.png"></image>
          <text class="menu-text">我的订单</text>
          <view class="menu-badge" wx:if="{{orderCount > 0}}">{{orderCount}}</view>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToFavorites">
          <image class="menu-icon" src="/images/6.png"></image>
          <text class="menu-text">我的收藏</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToAddressManage">
          <image class="menu-icon" src="/images/9.png"></image>
          <text class="menu-text">地址管理</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
      </view>
    </view>

    <!-- 设置菜单 -->
    <view class="menu-group">
      <view class="menu-title">设置</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="goToSettings">
          <image class="menu-icon" src="/images/7.png"></image>
          <text class="menu-text">系统设置</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
        <view class="menu-item" bindtap="goToAbout">
          <image class="menu-icon" src="/images/3.png"></image>
          <text class="menu-text">关于我们</text>
          <image class="menu-arrow" src="/images/arrow.svg"></image>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="btn-logout btn-danger" bindtap="handleLogout">退出登录</button>
    </view>
  </view>
</view>
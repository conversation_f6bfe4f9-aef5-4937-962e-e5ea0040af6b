/**user-center.wxss**/

.container {
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 60rpx 30rpx 40rpx;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
  color: white;
  min-width: 0; /* 确保flex子项能够正确收缩 */
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-phone {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 15rpx;
}

.user-role {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
}

.role-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: white;
  white-space: nowrap;
  min-width: 80rpx;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateZ(0);
}

.role-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.role-tag:hover::before {
  left: 100%;
}

.role-tag::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}

.role-tag.admin {
  background: linear-gradient(135deg, #FF6B6B 0%, #E74C3C 100%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.role-tag.user {
  background: linear-gradient(135deg, #4ECDC4 0%, #28A745 100%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.role-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
  display: inline-block;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}

.role-text {
  font-size: 22rpx;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  color: white;
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.login-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.btn-login {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  color: white;
}

/* 菜单区域 */
.menu-section {
  padding: 0 20rpx;
}

.menu-group {
  margin-bottom: 30rpx;
}

.menu-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 0 15rpx;
}

.menu-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-badge {
  background-color: #E74C3C;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-right: 15rpx;
  min-width: 30rpx;
  text-align: center;
}

.menu-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 退出登录 */
.logout-section {
  padding: 40rpx 20rpx;
}

.btn-logout {
  width: 100%;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
}
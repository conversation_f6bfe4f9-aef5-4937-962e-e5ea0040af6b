.container {
  width: 100%;
  height: 100%;
  align-items: center;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.main {
  width: 90%;
  display: flex;
  flex-direction: column;
  font-family: PingFang SC;
}

.tip {
  font-size: 23rpx;
  color: rgba(0, 0, 0, 0.5);
  text-align: center;
  margin: 30rpx auto 0 auto;
}

.top_tip {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.6);
  text-align: left;
  margin: 30rpx auto 0;
}

.box_text {
  background-color: white;
  text-align: center;
  padding: 300rpx 0;
  margin-top: 30rpx;
  color: rgba(0, 0, 0, 0.5);
}

.button {
  width: 450rpx;
  text-align: center;
  margin: 60rpx auto 0 auto;
  height: 80rpx;
  color: white;
  border-radius: 5px;
  line-height: 80rpx;
  background-color: #07c160;
}

.button_phone {
  text-align: center;
  margin: 250rpx auto 0 auto;
  color: white;
  border-radius: 5px;
  background-color: #07c160;
  font-weight: normal;
}

.button_clear {
  width: 300rpx;
  text-align: center;
  margin: 250rpx auto 0 auto;
  height: 80rpx;
  color: #07c160;
  border-radius: 5px;
  line-height: 80rpx;
  background-color: rgba(0, 0, 0, 0.03);
}

.line {
  height: 1rpx;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

.code_box {
  text-align: center;
  background-color: white;
  margin-top: 30rpx;
  padding: 17rpx;
}

.code_box_title {
  color: rgba(0, 0, 0, 0.5);
  font-size: 26rpx;
  margin-bottom: 20rpx;
  text-align: left;
}

.code_box_record {
  display: flex;
}

.code_box_record_title {
  width: 33%;
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.5);
  padding: 20rpx 0;
}

.code_box_record_detail {
  width: 33%;
  font-size: 26rpx;
  padding: 20rpx 0;
}

.title {
  margin-top: 16px;
  font-size: 36rpx;
  font-weight: 500;
  color: #000;
}

.info {
  margin-top: 12px;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
  line-height: 52rpx;
}

.img {
  margin-top: 16px;
  width: 100%;
}

.code_img {
  width: 360rpx;
  height: 360rpx;
}

.page-section {
  width: 100%;
  text-align: left;
  margin: 20rpx auto 0;
  font-size: 28rpx;
}
.weui-cells__title {
  margin: 10rpx 0;
  color: rgba(0, 0, 0, 0.6);
}
.weui-cells_after-title {
  border: 1px solid #ddd;
  padding: 10rpx;
  color: rgba(0, 0, 0, 0.6);
}

.weui-cells_after-error {
 color: red;
}
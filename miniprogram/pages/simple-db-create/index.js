// 简化数据库创建工具
Page({
  data: {
    logs: [],
    creating: false,
    step: 0,
    totalSteps: 4
  },

  onLoad() {
    this.addLog('简化数据库创建工具已加载');
    this.addLog('此工具将尝试多种方式创建数据库数据');
  },

  // 开始创建数据库
  async startCreate() {
    if (this.data.creating) return;
    
    this.setData({ 
      creating: true, 
      step: 0,
      logs: []
    });
    
    this.addLog('=== 开始创建数据库数据 ===');
    
    try {
      // 方法1：尝试使用云开发
      await this.tryCloudDatabase();
      
    } catch (error) {
      this.addLog(`创建失败: ${error.message}`);
      
      // 显示备选方案
      this.showAlternatives();
    } finally {
      this.setData({ creating: false });
    }
  },

  // 尝试云开发数据库
  async tryCloudDatabase() {
    this.addLog('方法1: 尝试云开发数据库...');
    
    try {
      // 检查云开发是否可用
      if (!wx.cloud) {
        throw new Error('云开发不可用');
      }
      
      // 尝试初始化云开发（不指定环境ID）
      wx.cloud.init({
        traceUser: true
      });
      
      this.addLog('云开发初始化成功');
      
      // 获取数据库实例
      const db = wx.cloud.database();
      this.addLog('数据库实例获取成功');
      
      // 创建用户数据
      await this.createUsersSimple(db);
      
      // 创建服务数据
      await this.createServicesSimple(db);
      
      // 创建人员数据
      await this.createWorkersSimple(db);
      
      // 创建订单数据
      await this.createOrdersSimple(db);
      
      this.addLog('=== 数据库创建完成！===');
      
      wx.showModal({
        title: '创建成功',
        content: '数据库数据创建完成！\n请到云开发控制台查看数据。',
        showCancel: false
      });
      
    } catch (error) {
      this.addLog(`云开发方法失败: ${error.message}`);
      throw error;
    }
  },

  // 创建用户数据（简化版）
  async createUsersSimple(db) {
    this.setData({ step: 1 });
    this.addLog('步骤1/4: 创建用户数据...');
    
    const users = [
      {
        phone: '13800000000',
        name: '管理员',
        password: '123456',
        isAdmin: true,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000001',
        name: '普通用户',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      }
    ];

    for (let user of users) {
      try {
        const result = await db.collection('users').add({
          data: user
        });
        this.addLog(`✓ 用户 ${user.name} 创建成功 (ID: ${result._id})`);
      } catch (error) {
        // 如果是重复数据，忽略错误
        if (error.message.includes('duplicate') || error.message.includes('重复')) {
          this.addLog(`- 用户 ${user.name} 已存在`);
        } else {
          this.addLog(`✗ 用户 ${user.name} 创建失败: ${error.message}`);
        }
      }
    }
  },

  // 创建服务数据（简化版）
  async createServicesSimple(db) {
    this.setData({ step: 2 });
    this.addLog('步骤2/4: 创建服务数据...');
    
    const services = [
      {
        name: '家庭保洁',
        description: '专业的家庭清洁服务',
        price: 80,
        duration: 3,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['深度清洁', '专业工具'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      }
    ];

    for (let service of services) {
      try {
        const result = await db.collection('services').add({
          data: service
        });
        this.addLog(`✓ 服务 ${service.name} 创建成功 (ID: ${result._id})`);
      } catch (error) {
        if (error.message.includes('duplicate') || error.message.includes('重复')) {
          this.addLog(`- 服务 ${service.name} 已存在`);
        } else {
          this.addLog(`✗ 服务 ${service.name} 创建失败: ${error.message}`);
        }
      }
    }
  },

  // 创建人员数据（简化版）
  async createWorkersSimple(db) {
    this.setData({ step: 3 });
    this.addLog('步骤3/4: 创建人员数据...');
    
    const workers = [
      {
        name: '王阿姨',
        phone: '137****9876',
        age: 45,
        experience: 8,
        rating: 4.8,
        avatar: '/images/icons/avatar.png',
        skills: ['家庭保洁', '老人护理'],
        description: '有丰富的家政服务经验',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      }
    ];

    for (let worker of workers) {
      try {
        const result = await db.collection('workers').add({
          data: worker
        });
        this.addLog(`✓ 人员 ${worker.name} 创建成功 (ID: ${result._id})`);
      } catch (error) {
        if (error.message.includes('duplicate') || error.message.includes('重复')) {
          this.addLog(`- 人员 ${worker.name} 已存在`);
        } else {
          this.addLog(`✗ 人员 ${worker.name} 创建失败: ${error.message}`);
        }
      }
    }
  },

  // 创建订单数据（简化版）
  async createOrdersSimple(db) {
    this.setData({ step: 4 });
    this.addLog('步骤4/4: 创建订单数据...');
    
    const orders = [
      {
        orderNumber: 'HZ202401150001',
        serviceName: '家庭保洁',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-16 09:00-12:00',
        address: '杭州市西湖区文三路123号',
        totalPrice: 240,
        duration: 3,
        status: 'pending',
        statusText: '待确认',
        customerName: '张女士',
        customerPhone: '138****5678',
        notes: '请提前电话联系',
        createTime: new Date()
      }
    ];

    for (let order of orders) {
      try {
        const result = await db.collection('orders').add({
          data: order
        });
        this.addLog(`✓ 订单 ${order.orderNumber} 创建成功 (ID: ${result._id})`);
      } catch (error) {
        if (error.message.includes('duplicate') || error.message.includes('重复')) {
          this.addLog(`- 订单 ${order.orderNumber} 已存在`);
        } else {
          this.addLog(`✗ 订单 ${order.orderNumber} 创建失败: ${error.message}`);
        }
      }
    }
  },

  // 显示备选方案
  showAlternatives() {
    this.addLog('=== 备选方案 ===');
    this.addLog('1. 检查云开发环境是否正确开通');
    this.addLog('2. 确认网络连接正常');
    this.addLog('3. 使用提供的JSON文件作为数据库导出');
    this.addLog('4. 手动在云控制台创建数据');
    
    wx.showModal({
      title: '创建失败',
      content: '自动创建失败，请查看日志了解详情。\n\n可以使用提供的JSON文件作为数据库文件提交。',
      showCancel: false
    });
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] });
  },

  // 添加日志
  addLog(message) {
    const now = new Date();
    const time = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    const logs = this.data.logs;
    logs.push({
      time: time,
      message: message
    });
    
    this.setData({ logs: logs });
    console.log(`[${time}] ${message}`);
  }
});

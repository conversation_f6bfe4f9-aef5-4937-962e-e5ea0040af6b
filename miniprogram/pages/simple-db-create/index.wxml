<!--简化数据库创建工具-->
<view class="container">
  <view class="header">
    <text class="title">简化数据库创建</text>
    <text class="subtitle">一键创建数据库数据</text>
  </view>

  <view class="progress-section" wx:if="{{creating}}">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(step / totalSteps) * 100}}%"></view>
    </view>
    <text class="progress-text">步骤 {{step}}/{{totalSteps}}</text>
  </view>

  <view class="actions">
    <button class="btn btn-primary" bindtap="startCreate" disabled="{{creating}}">
      {{creating ? '创建中...' : '开始创建数据库'}}
    </button>
    
    <button class="btn btn-outline" bindtap="clearLogs">
      清空日志
    </button>
  </view>

  <view class="logs-section">
    <view class="logs-header">
      <text class="logs-title">创建日志</text>
      <text class="logs-count">{{logs.length}}条</text>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true" scroll-top="{{logs.length * 100}}">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      
      <view class="empty-logs" wx:if="{{logs.length === 0}}">
        <text>暂无日志</text>
      </view>
    </scroll-view>
  </view>

  <view class="tips">
    <text class="tips-title">💡 使用说明</text>
    <text class="tips-text">1. 点击"开始创建数据库"按钮</text>
    <text class="tips-text">2. 等待创建完成</text>
    <text class="tips-text">3. 到云开发控制台查看数据</text>
    <text class="tips-text">4. 如果失败，可使用提供的JSON文件</text>
  </view>
</view>

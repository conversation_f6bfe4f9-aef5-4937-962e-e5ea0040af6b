/* 简化数据库创建工具样式 */

.container {
  padding: 40rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.progress-section {
  margin-bottom: 40rpx;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #E9ECEF;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #4A90E2;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx;
  border: none;
}

.btn-primary {
  background-color: #4A90E2;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #4A90E2;
  border: 2rpx solid #4A90E2;
}

.btn[disabled] {
  background-color: #CED4DA !important;
  color: #6C757D !important;
  border-color: #CED4DA !important;
}

.logs-section {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #F8F9FA;
  border-bottom: 1rpx solid #E9ECEF;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.logs-count {
  font-size: 24rpx;
  color: #666;
  background-color: #E9ECEF;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.logs-container {
  height: 400rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  flex-shrink: 0;
  width: 120rpx;
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

.log-message {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin-left: 20rpx;
}

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}

.tips {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

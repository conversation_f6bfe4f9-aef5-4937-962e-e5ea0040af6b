/**assign-order.wxss**/

.container {
  padding: 20rpx 20rpx 40rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 服务人员信息 */
.worker-info {
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.worker-item {
  display: flex;
  align-items: center;
}

.worker-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.worker-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.worker-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.worker-phone {
  font-size: 26rpx;
  color: #4A90E2;
  margin-bottom: 15rpx;
}

.worker-skills {
  display: flex;
  flex-wrap: wrap;
}

.skill-tag {
  background-color: #4A90E2;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

/* 订单区域 */
.orders-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 筛选器 */
.filter-section {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}

.picker {
  flex: 1;
  padding: 15rpx 20rpx;
  background-color: white;
  border-radius: 8rpx;
  border: 1rpx solid #E9ECEF;
  font-size: 28rpx;
  color: #333;
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.order-item {
  padding: 25rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1rpx solid #E9ECEF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.order-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
}

.status-pending {
  background-color: #FFC107;
}

.order-service {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.service-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.service-time {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.service-address {
  font-size: 24rpx;
  color: #999;
}

.order-customer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.customer-name,
.customer-phone {
  font-size: 26rpx;
  color: #666;
}

.order-price {
  text-align: right;
}

.price-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #CCC;
}

/* 确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #E9ECEF;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.modal-body {
  padding: 30rpx;
}

.assign-info {
  margin-bottom: 25rpx;
}

.assign-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.worker-name-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #4A90E2;
}

.order-summary {
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.order-title {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.order-detail {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #E9ECEF;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: transparent;
}

.btn-cancel {
  color: #666;
  border-right: 1rpx solid #E9ECEF;
}

.btn-confirm {
  color: #4A90E2;
  font-weight: bold;
}

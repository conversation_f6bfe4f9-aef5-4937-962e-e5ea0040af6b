<!--assign-order.wxml-->
<view class="container">
  <!-- 服务人员信息 -->
  <view class="worker-info card" wx:if="{{worker}}">
    <view class="section-title">服务人员</view>
    <view class="worker-item">
      <image class="worker-avatar" src="{{worker.avatar}}" mode="aspectFill"></image>
      <view class="worker-content">
        <text class="worker-name">{{worker.name}}</text>
        <text class="worker-phone">{{worker.phone}}</text>
        <view class="worker-skills">
          <text class="skill-tag" wx:for="{{worker.skills}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 可分配订单列表 -->
  <view class="orders-section card">
    <view class="section-title">可分配订单</view>
    
    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-item">
        <text class="filter-label">服务类型：</text>
        <picker bindchange="onServiceTypeChange" value="{{selectedServiceTypeIndex}}" range="{{serviceTypes}}">
          <view class="picker">
            {{serviceTypes[selectedServiceTypeIndex] || '全部'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" wx:if="{{filteredOrders.length > 0}}">
      <view class="order-item" wx:for="{{filteredOrders}}" wx:key="id" bindtap="selectOrder" data-order="{{item}}">
        <view class="order-header">
          <text class="order-number">{{item.orderNumber}}</text>
          <text class="order-status status-{{item.status}}">{{item.statusText}}</text>
        </view>
        
        <view class="order-service">
          <image class="service-image" src="{{item.serviceImage}}" mode="aspectFill"></image>
          <view class="service-info">
            <text class="service-name">{{item.serviceName}}</text>
            <text class="service-time">{{item.serviceTime}}</text>
            <text class="service-address">{{item.address}}</text>
          </view>
        </view>
        
        <view class="order-customer">
          <text class="customer-name">客户：{{item.customerName}}</text>
          <text class="customer-phone">{{item.customerPhone}}</text>
        </view>
        
        <view class="order-price">
          <text class="price-text">¥{{item.totalPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/5.png"></image>
      <text class="empty-text">暂无可分配的订单</text>
      <text class="empty-desc">当前没有适合该服务人员的待分配订单</text>
    </view>
  </view>
</view>

<!-- 分配确认弹窗 -->
<view class="modal-overlay" wx:if="{{showConfirmModal}}" bindtap="hideConfirmModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认分配订单</text>
    </view>
    
    <view class="modal-body">
      <view class="assign-info">
        <text class="assign-text">将订单分配给：</text>
        <text class="worker-name-text">{{worker.name}}</text>
      </view>
      
      <view class="order-summary" wx:if="{{selectedOrder}}">
        <text class="order-title">订单信息：</text>
        <text class="order-detail">{{selectedOrder.orderNumber}}</text>
        <text class="order-detail">{{selectedOrder.serviceName}}</text>
        <text class="order-detail">{{selectedOrder.serviceTime}}</text>
        <text class="order-detail">客户：{{selectedOrder.customerName}}</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn-cancel" bindtap="hideConfirmModal">取消</button>
      <button class="btn-confirm btn-primary" bindtap="confirmAssign">确认分配</button>
    </view>
  </view>
</view>

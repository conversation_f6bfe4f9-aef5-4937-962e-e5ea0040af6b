// assign-order.js
const DataManager = require('../../utils/data.js');

Page({
  data: {
    workerId: null,
    worker: null,
    orders: [],
    filteredOrders: [],
    serviceTypes: ['全部', '家庭保洁', '月嫂服务', '老人陪护', '家电维修', '钟点工服务', '育儿嫂服务'],
    selectedServiceTypeIndex: 0,
    showConfirmModal: false,
    selectedOrder: null
  },

  onLoad(options) {
    console.log('订单分配页面参数:', options);
    
    if (options.workerId) {
      this.setData({
        workerId: options.workerId
      });
      this.loadWorkerInfo();
      this.loadAvailableOrders();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载服务人员信息
  loadWorkerInfo() {
    DataManager.init();
    const workerId = parseInt(this.data.workerId);
    const worker = DataManager.getWorkerById(workerId);
    
    if (worker) {
      this.setData({
        worker: worker
      });
      wx.setNavigationBarTitle({
        title: `为${worker.name}分配订单`
      });
    } else {
      wx.showToast({
        title: '服务人员不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载可分配的订单
  loadAvailableOrders() {
    const allOrders = DataManager.getOrders();
    
    // 筛选出待分配的订单（状态为pending且未分配服务人员）
    const availableOrders = allOrders.filter(order => 
      order.status === 'pending' && !order.workerName
    );
    
    console.log('可分配的订单:', availableOrders);
    
    this.setData({
      orders: availableOrders,
      filteredOrders: availableOrders
    });
  },

  // 服务类型筛选
  onServiceTypeChange(e) {
    const index = e.detail.value;
    this.setData({
      selectedServiceTypeIndex: index
    });
    this.filterOrders();
  },

  // 筛选订单
  filterOrders() {
    const { orders, selectedServiceTypeIndex, serviceTypes } = this.data;
    let filtered = orders;
    
    // 按服务类型筛选
    if (selectedServiceTypeIndex > 0) {
      const selectedType = serviceTypes[selectedServiceTypeIndex];
      filtered = filtered.filter(order => order.serviceName === selectedType);
    }
    
    this.setData({
      filteredOrders: filtered
    });
  },

  // 选择订单
  selectOrder(e) {
    const order = e.currentTarget.dataset.order;
    console.log('选择的订单:', order);
    
    this.setData({
      selectedOrder: order,
      showConfirmModal: true
    });
  },

  // 隐藏确认弹窗
  hideConfirmModal() {
    this.setData({
      showConfirmModal: false,
      selectedOrder: null
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 确认分配订单
  confirmAssign() {
    const { worker, selectedOrder } = this.data;
    
    if (!worker || !selectedOrder) {
      wx.showToast({
        title: '信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '分配中...'
    });

    try {
      // 分配订单给服务人员
      const success = DataManager.assignWorkerToOrder(selectedOrder.id, worker);
      
      wx.hideLoading();
      
      if (success) {
        wx.showModal({
          title: '分配成功',
          content: `订单 ${selectedOrder.orderNumber} 已成功分配给 ${worker.name}`,
          showCancel: false,
          success: () => {
            // 隐藏弹窗
            this.hideConfirmModal();
            
            // 重新加载可分配订单列表
            this.loadAvailableOrders();
            
            // 显示成功提示
            wx.showToast({
              title: '分配成功',
              icon: 'success'
            });
          }
        });
      } else {
        wx.showToast({
          title: '分配失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('分配订单失败:', error);
      wx.showToast({
        title: '分配失败',
        icon: 'none'
      });
    }
  }
});

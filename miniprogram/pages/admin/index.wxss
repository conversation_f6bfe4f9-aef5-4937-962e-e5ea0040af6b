/**admin.wxss**/

.container {
  padding: 20rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 数据概览 */
.overview-section {
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stats-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stats-trend {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.stats-trend.positive {
  background-color: #E8F5E8;
  color: #28A745;
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-text {
  font-size: 26rpx;
  color: #4A90E2;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.action-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 26rpx;
  color: #333;
}

/* 最新订单 */
.recent-orders {
  margin-bottom: 30rpx;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.order-item {
  padding: 25rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.order-content {
  margin-bottom: 15rpx;
}

.service-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.customer-name,
.order-time {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

.btn-handle {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  background-color: #4A90E2;
  color: white;
  text-align: center;
  line-height: 1;
}

.btn-handle:active {
  background-color: #357ABD;
}

/* 服务人员状态 */
.worker-status {
  margin-bottom: 30rpx;
}

.worker-summary {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.summary-item {
  text-align: center;
}

.summary-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
}

/* 系统设置 */
.system-settings {
  margin-bottom: 30rpx;
}

.setting-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.setting-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

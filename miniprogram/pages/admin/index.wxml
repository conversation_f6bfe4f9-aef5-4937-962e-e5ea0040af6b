<!--admin.wxml-->
<view class="container">
  <!-- 数据概览 -->
  <view class="overview-section">
    <view class="overview-title">数据概览</view>
    <view class="stats-grid">
      <view class="stats-card">
        <view class="stats-number">{{stats.totalServices}}</view>
        <view class="stats-label">服务项目</view>
        <view class="stats-trend positive">+{{stats.servicesTrend}}</view>
      </view>
      <view class="stats-card">
        <view class="stats-number">{{stats.totalWorkers}}</view>
        <view class="stats-label">服务人员</view>
        <view class="stats-trend positive">+{{stats.workersTrend}}</view>
      </view>
      <view class="stats-card">
        <view class="stats-number">{{stats.totalOrders}}</view>
        <view class="stats-label">总订单</view>
        <view class="stats-trend positive">+{{stats.ordersTrend}}</view>
      </view>
      <view class="stats-card">
        <view class="stats-number">{{stats.totalRevenue}}</view>
        <view class="stats-label">总收入(元)</view>
        <view class="stats-trend positive">+{{stats.revenueTrend}}</view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="goToServiceManage">
        <image class="action-icon" src="/images/icons/business.png"></image>
        <text class="action-text">服务管理</text>
      </view>
      <view class="action-item" bindtap="goToWorkerManage">
        <image class="action-icon" src="/images/icons/avatar.png"></image>
        <text class="action-text">人员管理</text>
      </view>
      <view class="action-item" bindtap="goToOrderManage">
        <image class="action-icon" src="/images/icons/goods.png"></image>
        <text class="action-text">订单管理</text>
      </view>
      <view class="action-item" bindtap="addService">
        <image class="action-icon" src="/images/icons/copy.png"></image>
        <text class="action-text">添加服务</text>
      </view>
      <view class="action-item" bindtap="addWorker">
        <image class="action-icon" src="/images/icons/avatar.png"></image>
        <text class="action-text">添加人员</text>
      </view>
      <view class="action-item" bindtap="addOrder">
        <image class="action-icon" src="/images/icons/goods.png"></image>
        <text class="action-text">创建订单</text>
      </view>
    </view>
  </view>

  <!-- 最新订单 -->
  <view class="recent-orders">
    <view class="section-title">
      <text class="title-text">最新订单</text>
      <text class="more-text" bindtap="goToOrderManage">查看全部 ></text>
    </view>
    <view class="order-list">
      <view class="order-item card" wx:for="{{recentOrders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <text class="order-number">{{item.orderNumber}}</text>
          <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
        </view>
        <view class="order-content">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="customer-name">客户：{{item.customerName}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-footer">
          <text class="order-price">¥{{item.totalPrice}}</text>
          <button class="btn-handle btn-primary" wx:if="{{item.status === 'pending'}}" bindtap="handleOrder" data-id="{{item.id}}" catchtap="true">处理</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 服务人员状态 -->
  <view class="worker-status">
    <view class="section-title">
      <text class="title-text">服务人员状态</text>
      <text class="more-text" bindtap="goToWorkerManage">查看全部 ></text>
    </view>
    <view class="worker-summary">
      <view class="summary-item">
        <text class="summary-number">{{workerSummary.available}}</text>
        <text class="summary-label">空闲中</text>
      </view>
      <view class="summary-item">
        <text class="summary-number">{{workerSummary.busy}}</text>
        <text class="summary-label">服务中</text>
      </view>
      <view class="summary-item">
        <text class="summary-number">{{workerSummary.unavailable}}</text>
        <text class="summary-label">休假中</text>
      </view>
    </view>
  </view>

  <!-- 系统设置 -->
  <view class="system-settings">
    <view class="section-title">系统设置</view>
    <view class="setting-list">
      <view class="setting-item" bindtap="goToUserManage">
        <image class="setting-icon" src="/images/icons/usercenter.png"></image>
        <text class="setting-text">用户管理</text>
        <image class="arrow-icon" src="/images/arrow.svg"></image>
      </view>
      <view class="setting-item" bindtap="goToSystemConfig">
        <image class="setting-icon" src="/images/icons/setting.svg"></image>
        <text class="setting-text">系统配置</text>
        <image class="arrow-icon" src="/images/arrow.svg"></image>
      </view>
      <view class="setting-item" bindtap="exportData">
        <image class="setting-icon" src="/images/icons/share.svg"></image>
        <text class="setting-text">数据导出</text>
        <image class="arrow-icon" src="/images/arrow.svg"></image>
      </view>
    </view>
  </view>
</view>

const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    stats: {
      totalServices: 12,
      servicesTrend: 2,
      totalWorkers: 25,
      workersTrend: 3,
      totalOrders: 156,
      ordersTrend: 15,
      totalRevenue: 45680,
      revenueTrend: 5200
    },
    recentOrders: [],
    workerSummary: {
      available: 0,
      busy: 0,
      unavailable: 0
    }
  },

  onLoad() {
    this.checkAdminAuth();
    this.loadData();
  },

  onShow() {
    this.checkAdminAuth();
    this.loadData();
  },

  // 检查管理员权限
  checkAdminAuth() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }

    if (!userInfo.isAdmin) {
      wx.showModal({
        title: '权限不足',
        content: '您没有访问管理后台的权限',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
      return;
    }

    this.setData({
      userInfo: userInfo
    });
  },

  // 加载数据
  loadData() {
    this.loadRecentOrders();
    this.loadWorkerSummary();
  },

  // 加载最新订单
  loadRecentOrders() {
    const orders = DataManager.getRecentOrders();
    this.setData({
      recentOrders: orders
    });
  },

  // 加载服务人员状态汇总
  loadWorkerSummary() {
    const stats = DataManager.getStats();
    this.setData({
      workerSummary: {
        available: stats.availableWorkers,
        busy: stats.busyWorkers,
        unavailable: stats.unavailableWorkers
      }
    });
  },

  // 跳转到服务管理
  goToServiceManage() {
    wx.switchTab({
      url: '/pages/services/index'
    });
  },

  // 跳转到人员管理
  goToWorkerManage() {
    wx.navigateTo({
      url: '/pages/workers/index'
    });
  },

  // 跳转到订单管理
  goToOrderManage() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 添加服务
  addService() {
    wx.navigateTo({
      url: '/pages/service-manage/index'
    });
  },

  // 添加人员
  addWorker() {
    wx.navigateTo({
      url: '/pages/worker-manage/index'
    });
  },

  // 创建订单
  addOrder() {
    wx.navigateTo({
      url: '/pages/order-manage/index'
    });
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/index?id=${id}`
    });
  },

  // 处理订单
  handleOrder(e) {
    const id = e.currentTarget.dataset.id;
    const order = this.data.recentOrders.find(o => o.id === id);

    wx.showActionSheet({
      itemList: ['确认订单', '分配人员', '查看详情'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.confirmOrder(id);
            break;
          case 1:
            this.assignWorker(id);
            break;
          case 2:
            this.goToOrderDetail({ currentTarget: { dataset: { id } } });
            break;
        }
      }
    });
  },

  // 确认订单
  confirmOrder(id) {
    wx.showModal({
      title: '确认订单',
      content: '确定要确认此订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const orders = this.data.recentOrders.map(o => {
            if (o.id === id) {
              return { ...o, status: 'confirmed', statusText: '已确认' };
            }
            return o;
          });

          this.setData({
            recentOrders: orders
          });

          wx.showToast({
            title: '订单已确认',
            icon: 'success'
          });
        }
      }
    });
  },

  // 分配服务人员
  assignWorker(id) {
    wx.navigateTo({
      url: `/pages/workers/index?selectMode=true&orderId=${id}`
    });
  },

  // 用户管理
  goToUserManage() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 系统配置
  goToSystemConfig() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 数据导出
  exportData() {
    wx.showModal({
      title: '数据导出',
      content: '确定要导出系统数据吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '导出中...'
          });

          // 模拟导出过程
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '导出成功',
              icon: 'success'
            });
          }, 2000);
        }
      }
    });
  }
});

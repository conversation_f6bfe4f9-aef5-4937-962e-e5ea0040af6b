/**service-detail.wxss**/

.container {
  padding-bottom: 120rpx;
}

/* 服务图片 */
.service-image-section {
  margin-bottom: 20rpx;
}

.service-image {
  width: 100%;
  height: 400rpx;
}

/* 服务信息 */
.service-info {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.service-status {
  margin-left: 15rpx;
}

.service-price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.service-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-right: 10rpx;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.service-duration {
  font-size: 26rpx;
  color: #666;
  background-color: #F8F9FA;
  padding: 8rpx 12rpx;
  border-radius: 4rpx;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-tag {
  background-color: #4A90E2;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

/* 服务描述 */
.service-description {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 服务流程 */
.service-process {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.process-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.process-item {
  display: flex;
  align-items: flex-start;
}

.process-step {
  width: 40rpx;
  height: 40rpx;
  background-color: #4A90E2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.process-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 注意事项 */
.service-notes {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.note-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  border-top: 1rpx solid #E9ECEF;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-book,
.btn-disabled {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.btn-disabled {
  background-color: #CED4DA;
  color: #6C757D;
}

.admin-actions {
  display: flex;
  gap: 15rpx;
}

.btn-edit {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}



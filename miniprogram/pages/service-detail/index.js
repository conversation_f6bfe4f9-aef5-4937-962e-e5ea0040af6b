const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    serviceId: null,
    service: null
  },

  onLoad(options) {
    this.checkUserLogin();

    if (options.id) {
      this.setData({
        serviceId: options.id
      });
      this.loadServiceDetail();
    }
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载服务详情
  async loadServiceDetail() {
    const serviceId = this.data.serviceId;
    console.log('加载服务详情，原始ID:', serviceId);

    let service = null;

    try {
      // 首先尝试从数据库获取
      service = await this.getServiceFromDatabase(serviceId);
      console.log('从数据库获取的服务:', service);
    } catch (error) {
      console.log('数据库获取失败，尝试模拟数据:', error);
    }

    // 如果数据库没有找到，尝试模拟数据
    if (!service) {
      service = this.getServiceFromMockData(serviceId);
      console.log('从模拟数据获取的服务:', service);
    }

    if (service) {
      // 为服务添加详细信息
      const detailedService = {
        ...service,
        process: this.getServiceProcess(service.id || 1),
        notes: this.getServiceNotes(service.id || 1)
      };

      this.setData({
        service: detailedService
      });
      wx.setNavigationBarTitle({
        title: service.name
      });
    } else {
      console.error('服务不存在，ID:', serviceId);
      wx.showToast({
        title: '服务不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 从数据库获取服务
  async getServiceFromDatabase(serviceId) {
    try {
      const db = wx.cloud.database();
      const result = await db.collection('services').doc(serviceId).get();

      if (result.data) {
        // 转换数据库数据格式
        return {
          id: result.data._id,
          name: result.data.name,
          description: result.data.description,
          price: result.data.price,
          duration: result.data.duration,
          category: result.data.category,
          image: result.data.image || '/images/default-goods-image.png',
          tags: result.data.tags || [],
          status: result.data.status || 'active',
          statusText: result.data.statusText || '可预约',
          audioIntro: result.data.audioIntro
        };
      }
    } catch (error) {
      console.log('数据库查询失败:', error);
      return null;
    }
    return null;
  },

  // 从模拟数据获取服务
  getServiceFromMockData(serviceId) {
    // 尝试不同的ID格式匹配
    let service = DataManager.getServiceById(serviceId);

    if (!service && !isNaN(serviceId)) {
      // 如果是数字字符串，尝试转换为数字
      service = DataManager.getServiceById(parseInt(serviceId));
    }

    if (!service) {
      // 如果还是找不到，尝试字符串匹配
      service = DataManager.getServiceById(serviceId.toString());
    }

    // 如果还是找不到，尝试按名称匹配（容错处理）
    if (!service) {
      const services = DataManager.getServicesSync();
      service = services[0]; // 返回第一个服务作为默认
      console.log('使用默认服务:', service);
    }

    return service;
  },

  // 获取服务流程
  getServiceProcess(serviceId) {
    const processes = {
      1: [
        '上门前电话确认服务时间',
        '携带专业清洁工具和环保清洁剂',
        '按照标准流程进行全面清洁',
        '清洁完成后进行质量检查',
        '客户验收并签字确认'
      ],
      2: [
        '服务前详细了解客户需求',
        '制定个性化护理方案',
        '24小时贴心护理服务',
        '定期健康状况评估',
        '服务结束后提供护理建议'
      ],
      3: [
        '了解老人身体状况和需求',
        '制定个性化护理计划',
        '提供日常生活照料',
        '进行健康状况监护',
        '记录护理情况并反馈'
      ]
    };
    return processes[serviceId] || [];
  },

  // 获取注意事项
  getServiceNotes(serviceId) {
    const notes = {
      1: [
        '请提前预约，确保服务人员安排',
        '服务期间请保持室内通风',
        '贵重物品请提前收好',
        '如有特殊要求请提前说明'
      ],
      2: [
        '需要提前1-2周预约',
        '服务期间需要准备相关用品',
        '如有特殊情况请及时沟通',
        '服务费用按天计算'
      ],
      3: [
        '需要了解老人的身体状况',
        '如有慢性疾病请提前告知',
        '紧急情况下会及时联系家属',
        '护理人员会定期汇报情况'
      ]
    };
    return notes[serviceId] || [];
  },



  // 预约服务
  bookService() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '预约服务需要先登录账号',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/order-manage/index?serviceId=${this.data.serviceId}`
    });
  },

  // 编辑服务
  editService() {
    wx.navigateTo({
      url: `/pages/service-manage/index?id=${this.data.serviceId}`
    });
  },


});

<!--service-detail.wxml-->
<view class="container">
  <view class="service-detail" wx:if="{{service}}">
    <!-- 服务图片 -->
    <view class="service-image-section">
      <image class="service-image" src="{{service.image}}" mode="aspectFill"></image>
    </view>

    <!-- 服务信息 -->
    <view class="service-info card">
      <view class="service-header">
        <text class="service-name">{{service.name}}</text>
        <view class="service-status">
          <text class="status-tag status-{{service.status}}">{{service.statusText}}</text>
        </view>
      </view>
      
      <view class="service-price-info">
        <text class="service-price">¥{{service.price}}</text>
        <text class="price-unit">/次</text>
        <text class="service-duration">{{service.duration}}小时</text>
      </view>
      
      <view class="service-tags">
        <text class="service-tag" wx:for="{{service.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>

    <!-- 服务描述 -->
    <view class="service-description card">
      <view class="section-title">服务描述</view>
      <text class="description-text">{{service.description}}</text>
    </view>

    <!-- 服务流程 -->
    <view class="service-process card" wx:if="{{service.process}}">
      <view class="section-title">服务流程</view>
      <view class="process-list">
        <view class="process-item" wx:for="{{service.process}}" wx:key="index">
          <view class="process-step">{{index + 1}}</view>
          <text class="process-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 注意事项 -->
    <view class="service-notes card" wx:if="{{service.notes}}">
      <view class="section-title">注意事项</view>
      <view class="notes-list">
        <text class="note-item" wx:for="{{service.notes}}" wx:key="index">• {{item}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn-book btn-primary" wx:if="{{service.status === 'active'}}" bindtap="bookService">立即预约</button>
      <button class="btn-disabled" wx:else disabled>暂不可预约</button>
      
      <!-- 管理员操作 -->
      <view class="admin-actions" wx:if="{{userInfo.isAdmin}}">
        <button class="btn-edit btn-secondary" bindtap="editService">编辑服务</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:else>
    <text class="loading-text">加载中...</text>
  </view>
</view>

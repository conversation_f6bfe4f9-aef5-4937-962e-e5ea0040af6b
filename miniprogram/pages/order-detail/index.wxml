<!--order-detail.wxml-->
<view class="container">
  <view class="order-detail" wx:if="{{order}}">
    <!-- 订单状态 -->
    <view class="order-status-section card">
      <view class="status-header">
        <text class="status-title">订单状态</text>
        <text class="status-tag status-{{order.status}}">{{order.statusText}}</text>
      </view>
      <view class="order-number">订单号：{{order.orderNumber}}</view>
      <view class="order-time">下单时间：{{order.createTime}}</view>
    </view>

    <!-- 服务信息 -->
    <view class="service-info-section card">
      <view class="section-title">服务信息</view>
      <view class="service-item">
        <image class="service-image" src="{{order.serviceImage}}" mode="aspectFill"></image>
        <view class="service-content">
          <text class="service-name">{{order.serviceName}}</text>
          <text class="service-time">服务时间：{{order.serviceTime}}</text>
          <text class="service-duration">服务时长：{{order.duration}}小时</text>
          <text class="service-price">服务费用：¥{{order.totalPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 服务地址 -->
    <view class="address-section card">
      <view class="section-title">服务地址</view>
      <view class="address-content">
        <text class="address-text">{{order.address}}</text>
        <text class="contact-info">联系人：{{order.customerName}} {{order.customerPhone}}</text>
      </view>
    </view>

    <!-- 服务人员信息 -->
    <view class="worker-info-section card" wx:if="{{order.workerName}}">
      <view class="section-title">服务人员</view>
      <view class="worker-item">
        <image class="worker-avatar" src="/images/icons/avatar.png" mode="aspectFill"></image>
        <view class="worker-content">
          <text class="worker-name">{{order.workerName}}</text>
          <text class="worker-phone">{{order.workerPhone}}</text>
          <text class="worker-skills">专业技能：家庭保洁、深度清洁</text>
        </view>
        <button class="btn-contact btn-secondary" bindtap="contactWorker">联系</button>
      </view>
    </view>

    <!-- 订单备注 -->
    <view class="notes-section card" wx:if="{{order.notes}}">
      <view class="section-title">订单备注</view>
      <text class="notes-text">{{order.notes}}</text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- 用户操作 -->
      <block wx:if="{{!userInfo.isAdmin}}">
        <button class="btn-cancel btn-danger" wx:if="{{order.status === 'pending'}}" bindtap="cancelOrder">取消订单</button>
        <button class="btn-confirm btn-primary" wx:if="{{order.status === 'in_progress'}}" bindtap="confirmComplete">确认完成</button>
        <button class="btn-evaluate btn-secondary" wx:if="{{order.status === 'completed'}}" bindtap="evaluateService">评价服务</button>
      </block>

      <!-- 管理员操作 -->
      <block wx:if="{{userInfo.isAdmin}}">
        <button class="btn-assign btn-primary" wx:if="{{order.status === 'pending' && !order.workerName}}" bindtap="assignWorker">分配人员</button>
        <button class="btn-confirm btn-primary" wx:if="{{order.status === 'pending' && order.workerName}}" bindtap="confirmOrder">确认订单</button>
        <button class="btn-edit btn-secondary" bindtap="editOrder">编辑订单</button>
      </block>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:else>
    <text class="loading-text">加载中...</text>
  </view>
</view>

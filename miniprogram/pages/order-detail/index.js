const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    orderId: null,
    order: null
  },

  onLoad(options) {
    this.checkUserLogin();

    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    }
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo: userInfo
    });
  },

  // 加载订单详情
  loadOrderDetail() {
    // 确保DataManager已初始化
    DataManager.init();

    const orderId = this.data.orderId;
    console.log('加载订单详情，订单ID:', orderId);

    const order = DataManager.getOrderById(orderId);
    console.log('找到的订单:', order);

    if (order) {
      // 为订单添加备注信息
      const detailedOrder = {
        ...order,
        notes: this.getOrderNotes(orderId)
      };

      this.setData({
        order: detailedOrder
      });
      wx.setNavigationBarTitle({
        title: `订单详情 - ${order.orderNumber}`
      });
    } else {
      wx.showToast({
        title: '订单不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取订单备注
  getOrderNotes(orderId) {
    const notes = {
      1: '请提前电话联系，家里有小孩需要注意安全。',
      2: '产妇刚生产，需要专业护理，请准备相关用品。',
      3: '老人有高血压，需要定时测量血压，注意饮食。'
    };
    return notes[orderId] || '';
  },

  // 联系服务人员
  contactWorker() {
    const order = this.data.order;
    if (!order.workerPhone) {
      wx.showToast({
        title: '暂无联系方式',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['拨打电话', '发送短信'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.makePhoneCall({
            phoneNumber: order.workerPhone.replace(/\*/g, '1') // 模拟真实号码
          });
        } else if (res.tapIndex === 1) {
          wx.showToast({
            title: '短信功能待开发',
            icon: 'none'
          });
        }
      }
    });
  },

  // 取消订单
  cancelOrder() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消此订单吗？取消后不可恢复。',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const order = { ...this.data.order };
          order.status = 'cancelled';
          order.statusText = '已取消';

          this.setData({
            order: order
          });

          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
        }
      }
    });
  },

  // 确认完成
  confirmComplete() {
    wx.showModal({
      title: '确认完成',
      content: '确认服务已完成？完成后将进入评价环节。',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const order = { ...this.data.order };
          order.status = 'completed';
          order.statusText = '已完成';

          this.setData({
            order: order
          });

          wx.showToast({
            title: '订单已完成',
            icon: 'success'
          });
        }
      }
    });
  },

  // 评价服务
  evaluateService() {
    wx.showToast({
      title: '评价功能待开发',
      icon: 'none'
    });
  },

  // 分配服务人员
  assignWorker() {
    wx.navigateTo({
      url: `/pages/workers/index?selectMode=true&orderId=${this.data.orderId}`
    });
  },

  // 确认订单
  confirmOrder() {
    wx.showModal({
      title: '确认订单',
      content: '确定要确认此订单吗？确认后将开始服务。',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用云函数更新订单状态
          const order = { ...this.data.order };
          order.status = 'confirmed';
          order.statusText = '已确认';

          this.setData({
            order: order
          });

          wx.showToast({
            title: '订单已确认',
            icon: 'success'
          });
        }
      }
    });
  },

  // 编辑订单
  editOrder() {
    wx.navigateTo({
      url: `/pages/order-manage/index?id=${this.data.orderId}`
    });
  }
});

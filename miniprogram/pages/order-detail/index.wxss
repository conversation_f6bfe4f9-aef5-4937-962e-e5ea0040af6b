/**order-detail.wxss**/

.container {
  padding: 0 0 120rpx 0;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 订单状态区域 */
.order-status-section {
  margin: 20rpx;
  padding: 30rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.order-number,
.order-time {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

/* 服务信息区域 */
.service-info-section {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.service-item {
  display: flex;
  align-items: flex-start;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-time,
.service-duration {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.service-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
  margin-top: 10rpx;
}

/* 地址区域 */
.address-section {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.address-content {
  display: flex;
  flex-direction: column;
}

.address-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.contact-info {
  font-size: 26rpx;
  color: #4A90E2;
}

/* 服务人员区域 */
.worker-info-section {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.worker-item {
  display: flex;
  align-items: center;
}

.worker-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.worker-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.worker-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.worker-phone {
  font-size: 26rpx;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.worker-skills {
  font-size: 24rpx;
  color: #666;
}

.btn-contact {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  margin-left: 15rpx;
}

/* 备注区域 */
.notes-section {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.notes-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  border-top: 1rpx solid #E9ECEF;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 15rpx;
}

.action-buttons button {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

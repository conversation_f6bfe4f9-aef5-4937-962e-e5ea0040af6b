/**order-manage.wxss**/

.container {
  padding: 20rpx 20rpx 120rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

.order-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 服务信息区域 */
.service-section,
.select-service {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
}

.service-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.service-price {
  font-size: 28rpx;
  color: #E74C3C;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.service-duration {
  font-size: 24rpx;
  color: #666;
  background-color: #F8F9FA;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.service-description {
  margin-top: 10rpx;
}

.description-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.btn-change {
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  margin-left: 15rpx;
  border-radius: 8rpx;
  border: none;
  background-color: #4A90E2;
  color: white;
}

.btn-change:active {
  background-color: #357ABD;
}

.btn-select {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 8rpx;
  border: none;
}

/* 预约信息区域 */
.booking-info {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #CED4DA;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: white;
  box-sizing: border-box;
  line-height: 80rpx;
  display: flex;
  align-items: center;
}

.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #CED4DA;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: white;
  min-height: 120rpx;
  box-sizing: border-box;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #CED4DA;
  border-radius: 8rpx;
  background-color: white;
  box-sizing: border-box;
}

.picker-text {
  font-size: 32rpx;
  color: #333;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 费用明细区域 */
.cost-detail {
  padding: 30rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cost-item:last-of-type {
  border-bottom: none;
}

.cost-label {
  font-size: 28rpx;
  color: #666;
}

.cost-value {
  font-size: 28rpx;
  color: #333;
}

.cost-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 10rpx;
  border-top: 2rpx solid #E9ECEF;
  margin-top: 15rpx;
}

.total-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #E74C3C;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  border-top: 1rpx solid #E9ECEF;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-submit {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 8rpx;
  border: none;
  background-color: #4A90E2;
  color: white;
}

.btn-submit:active {
  background-color: #357ABD;
}

.btn-submit[disabled] {
  background-color: #CED4DA !important;
  color: #6C757D !important;
}

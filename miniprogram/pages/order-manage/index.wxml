<!--order-manage.wxml-->
<view class="container">
  <view class="order-form">
    <!-- 服务信息 -->
    <view class="service-section card" wx:if="{{selectedService}}">
      <view class="section-title">{{serviceId ? '预约服务' : '选择的服务'}}</view>
      <view class="service-item">
        <image class="service-image" src="{{selectedService.image}}" mode="aspectFill"></image>
        <view class="service-content">
          <text class="service-name">{{selectedService.name}}</text>
          <text class="service-price">¥{{selectedService.price}}/次</text>
          <text class="service-duration">{{selectedService.duration}}小时</text>
          <view class="service-description" wx:if="{{selectedService.description}}">
            <text class="description-text">{{selectedService.description}}</text>
          </view>
        </view>
        <!-- 只有在非预约模式下才显示更换按钮 -->
        <button class="btn-change btn-secondary" wx:if="{{!serviceId}}" bindtap="changeService">更换</button>
      </view>
    </view>

    <!-- 选择服务 -->
    <view class="select-service card" wx:else>
      <view class="section-title">选择服务</view>
      <button class="btn-select btn-primary" bindtap="selectService">点击选择服务项目</button>
    </view>

    <!-- 预约信息 -->
    <view class="booking-info card">
      <view class="section-title">预约信息</view>

      <view class="form-group">
        <text class="form-label">服务时间</text>
        <picker mode="date" value="{{serviceDate}}" start="{{today}}" bindchange="onDateChange">
          <view class="picker-input">
            <text class="picker-text">{{serviceDate || '请选择日期'}}</text>
            <image class="picker-arrow" src="/images/arrow.svg"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">服务时段</text>
        <picker mode="time" value="{{serviceTime}}" bindchange="onTimeChange">
          <view class="picker-input">
            <text class="picker-text">{{serviceTime || '请选择时间'}}</text>
            <image class="picker-arrow" src="/images/arrow.svg"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">联系人</text>
        <input class="form-input" placeholder="请输入联系人姓名" value="{{contactName}}" bindinput="onContactNameInput" />
      </view>

      <view class="form-group">
        <text class="form-label">联系电话</text>
        <input class="form-input" type="number" placeholder="请输入联系电话" value="{{contactPhone}}" bindinput="onContactPhoneInput" maxlength="11" />
      </view>

      <view class="form-group">
        <text class="form-label">服务地址</text>
        <textarea class="form-textarea" placeholder="请输入详细的服务地址" value="{{serviceAddress}}" bindinput="onAddressInput" maxlength="200"></textarea>
      </view>

      <view class="form-group">
        <text class="form-label">备注说明</text>
        <textarea class="form-textarea" placeholder="请输入特殊要求或备注信息（选填）" value="{{notes}}" bindinput="onNotesInput" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="cost-detail card" wx:if="{{selectedService}}">
      <view class="section-title">费用明细</view>
      <view class="cost-item">
        <text class="cost-label">服务费用</text>
        <text class="cost-value">¥{{selectedService.price}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">服务时长</text>
        <text class="cost-value">{{selectedService.duration}}小时</text>
      </view>
      <view class="cost-total">
        <text class="total-label">总计</text>
        <text class="total-value">¥{{totalCost}}</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="btn-submit btn-primary" bindtap="submitOrder" disabled="{{!canSubmit}}">
        {{isEdit ? '更新订单' : '提交预约'}}
      </button>
    </view>
  </view>
</view>

const DataManager = require('../../utils/data.js');

Page({
  data: {
    userInfo: null,
    isEdit: false,
    orderId: null,
    serviceId: null,
    selectedService: null,
    serviceDate: '',
    serviceTime: '',
    contactName: '',
    contactPhone: '',
    serviceAddress: '',
    notes: '',
    totalCost: 0,
    canSubmit: false,
    today: ''
  },

  onLoad(options) {
    this.checkUserLogin();
    this.initToday();

    if (options.id) {
      // 编辑订单
      this.setData({
        isEdit: true,
        orderId: options.id
      });
      wx.setNavigationBarTitle({
        title: '编辑订单'
      });
      this.loadOrderData();
    } else if (options.serviceId) {
      // 预约服务
      this.setData({
        serviceId: options.serviceId
      });
      wx.setNavigationBarTitle({
        title: '预约服务'
      });
      this.loadServiceData();
    } else {
      // 创建订单
      wx.setNavigationBarTitle({
        title: '创建订单'
      });
    }
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '预约服务需要先登录账号',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    this.setData({
      userInfo: userInfo,
      contactName: userInfo.name,
      contactPhone: userInfo.phone
    });
  },

  // 初始化今天日期
  initToday() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    this.setData({
      today: `${year}-${month}-${day}`
    });
  },

  // 加载服务数据
  async loadServiceData() {
    const serviceId = this.data.serviceId;
    console.log('加载服务数据，serviceId:', serviceId);

    let service = null;

    try {
      // 首先尝试从数据库获取
      service = await this.getServiceFromDatabase(serviceId);
      console.log('从数据库获取的服务:', service);
    } catch (error) {
      console.log('数据库获取失败，尝试模拟数据:', error);
    }

    // 如果数据库没有找到，尝试模拟数据
    if (!service) {
      service = this.getServiceFromMockData(serviceId);
      console.log('从模拟数据获取的服务:', service);
    }

    if (service) {
      this.setData({
        selectedService: service,
        totalCost: service.price
      });
      this.checkCanSubmit();

      wx.showToast({
        title: '服务信息已加载',
        icon: 'success',
        duration: 1500
      });
    } else {
      console.error('未找到服务，serviceId:', serviceId);
      wx.showToast({
        title: '服务信息加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 从数据库获取服务
  async getServiceFromDatabase(serviceId) {
    try {
      const db = wx.cloud.database();
      const result = await db.collection('services').doc(serviceId).get();

      if (result.data) {
        return {
          id: result.data._id,
          name: result.data.name,
          description: result.data.description,
          price: result.data.price,
          duration: result.data.duration,
          category: result.data.category,
          image: result.data.image || '/images/default-goods-image.png',
          tags: result.data.tags || [],
          status: result.data.status || 'active',
          statusText: result.data.statusText || '可预约'
        };
      }
    } catch (error) {
      console.log('数据库查询失败:', error);
      return null;
    }
    return null;
  },

  // 从模拟数据获取服务
  getServiceFromMockData(serviceId) {
    // 尝试不同的ID格式匹配
    let service = DataManager.getServiceById(serviceId);

    if (!service && !isNaN(serviceId)) {
      // 如果是数字字符串，尝试转换为数字
      service = DataManager.getServiceById(parseInt(serviceId));
    }

    if (!service) {
      // 如果还是找不到，尝试字符串匹配
      service = DataManager.getServiceById(serviceId.toString());
    }

    // 如果还是找不到，尝试按名称匹配（容错处理）
    if (!service) {
      const services = DataManager.getServicesSync();
      service = services[0]; // 返回第一个服务作为默认
      console.log('使用默认服务:', service);
    }

    return service;
  },

  // 加载订单数据（编辑模式）
  loadOrderData() {
    // 模拟加载订单数据
    wx.showToast({
      title: '编辑功能待完善',
      icon: 'none'
    });
  },

  // 选择服务
  selectService() {
    wx.navigateTo({
      url: '/pages/services/index?selectMode=true'
    });
  },

  // 更换服务
  changeService() {
    this.selectService();
  },

  // 日期选择
  onDateChange(e) {
    this.setData({
      serviceDate: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 时间选择
  onTimeChange(e) {
    this.setData({
      serviceTime: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 联系人输入
  onContactNameInput(e) {
    this.setData({
      contactName: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 联系电话输入
  onContactPhoneInput(e) {
    this.setData({
      contactPhone: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 地址输入
  onAddressInput(e) {
    this.setData({
      serviceAddress: e.detail.value
    });
    this.checkCanSubmit();
  },

  // 备注输入
  onNotesInput(e) {
    this.setData({
      notes: e.detail.value
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { selectedService, serviceDate, serviceTime, contactName, contactPhone, serviceAddress } = this.data;

    const canSubmit = selectedService &&
                     serviceDate &&
                     serviceTime &&
                     contactName.trim() &&
                     contactPhone.trim() &&
                     serviceAddress.trim();

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 提交订单
  submitOrder() {
    if (!this.data.canSubmit) return;

    const { selectedService, serviceDate, serviceTime, contactName, contactPhone, serviceAddress, notes, totalCost } = this.data;

    // 生成订单号
    const now = new Date();
    const orderNumber = `HZ${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

    // 创建完整的订单数据
    const orderData = {
      id: Date.now(), // 使用时间戳作为ID
      orderNumber: orderNumber,
      serviceId: selectedService.id,
      serviceName: selectedService.name,
      serviceImage: selectedService.image,
      serviceTime: `${serviceDate} ${serviceTime}`,
      address: serviceAddress,
      totalPrice: totalCost,
      duration: selectedService.duration,
      status: 'pending',
      statusText: '待确认',
      createTime: now.toLocaleString(),
      customerName: contactName,
      customerPhone: contactPhone,
      workerName: '',
      workerPhone: '',
      notes: notes
    };

    console.log('创建的订单数据:', orderData);

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交过程
    setTimeout(() => {
      wx.hideLoading();

      try {
        // 保存订单到DataManager
        DataManager.addOrder(orderData);
        console.log('订单已保存到DataManager');

        wx.showModal({
          title: '预约成功',
          content: `订单号：${orderNumber}\n我们会尽快安排服务人员与您联系。`,
          showCancel: false,
          success: () => {
            // 返回上一页并刷新数据
            wx.navigateBack({
              success: () => {
                // 通知其他页面刷新数据
                const pages = getCurrentPages();
                if (pages.length > 0) {
                  const prevPage = pages[pages.length - 1];
                  if (prevPage.loadData) {
                    prevPage.loadData();
                  }
                  if (prevPage.loadOrders) {
                    prevPage.loadOrders();
                  }
                }
              }
            });
          }
        });
      } catch (error) {
        console.error('保存订单失败:', error);
        wx.showToast({
          title: '订单保存失败',
          icon: 'none'
        });
      }
    }, 1500);
  }
});

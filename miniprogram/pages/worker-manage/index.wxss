/**worker-manage.wxss**/

.container {
  padding: 0;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background-color: white;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #E9ECEF;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 表单容器 */
.form-container {
  padding: 40rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input,
.form-textarea {
  width: 100%;
  height: 80rpx;
  padding: 20rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: white;
  box-sizing: border-box;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #4A90E2;
}

.form-textarea {
  height: auto;
  min-height: 120rpx;
  resize: none;
  align-items: flex-start;
  padding-top: 20rpx;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  align-items: center;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: #4A90E2;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.tag-text {
  margin-right: 10rpx;
}

.tag-remove {
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}

.tag-input {
  flex: 1;
  min-width: 200rpx;
  height: 60rpx;
  padding: 10rpx 20rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 20rpx;
  font-size: 24rpx;
  box-sizing: border-box;
}

/* 单选按钮组 */
.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.radio-text {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 60rpx;
}

.btn-submit,
.btn-cancel,
.btn-delete {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 8rpx;
  border: none;
}

.btn-submit {
  background-color: #4A90E2;
  color: white;
}

.btn-submit[disabled] {
  background-color: #CED4DA;
  color: #6C757D;
}

.btn-cancel {
  background-color: #6C757D;
  color: white;
}

.btn-delete {
  background-color: #DC3545;
  color: white;
}

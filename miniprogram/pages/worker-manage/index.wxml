<!--worker-manage.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">{{isEdit ? '编辑人员' : '添加人员'}}</text>
  </view>

  <!-- 人员表单 -->
  <view class="form-container">
    <form bindsubmit="handleSubmit">
      <!-- 姓名 -->
      <view class="form-group">
        <text class="form-label">姓名 *</text>
        <input
          class="form-input"
          name="name"
          placeholder="请输入姓名"
          value="{{formData.name}}"
          bindinput="onInputChange"
          data-field="name"
        />
      </view>

      <!-- 手机号 -->
      <view class="form-group">
        <text class="form-label">手机号 *</text>
        <input
          class="form-input"
          name="phone"
          type="number"
          placeholder="请输入手机号"
          value="{{formData.phone}}"
          bindinput="onInputChange"
          data-field="phone"
          maxlength="11"
        />
      </view>

      <!-- 年龄 -->
      <view class="form-group">
        <text class="form-label">年龄 *</text>
        <input
          class="form-input"
          name="age"
          type="number"
          placeholder="请输入年龄"
          value="{{formData.age}}"
          bindinput="onInputChange"
          data-field="age"
        />
      </view>

      <!-- 工作经验 -->
      <view class="form-group">
        <text class="form-label">工作经验 (年) *</text>
        <input
          class="form-input"
          name="experience"
          type="number"
          placeholder="请输入工作经验年数"
          value="{{formData.experience}}"
          bindinput="onInputChange"
          data-field="experience"
        />
      </view>

      <!-- 个人描述 -->
      <view class="form-group">
        <text class="form-label">个人描述</text>
        <textarea
          class="form-textarea"
          name="description"
          placeholder="请输入个人描述"
          value="{{formData.description}}"
          bindinput="onInputChange"
          data-field="description"
          maxlength="200"
        ></textarea>
      </view>

      <!-- 技能标签 -->
      <view class="form-group">
        <text class="form-label">技能标签</text>
        <view class="tags-container">
          <view class="tag-item" wx:for="{{formData.skills}}" wx:key="index">
            <text class="tag-text">{{item}}</text>
            <text class="tag-remove" bindtap="removeSkill" data-index="{{index}}">×</text>
          </view>
          <input
            class="tag-input"
            placeholder="输入技能后按回车"
            value="{{skillInput}}"
            bindinput="onSkillInput"
            bindconfirm="addSkill"
          />
        </view>
      </view>

      <!-- 工作状态 -->
      <view class="form-group">
        <text class="form-label">工作状态</text>
        <radio-group bindchange="onStatusChange">
          <label class="radio-item" wx:for="{{statusOptions}}" wx:key="value">
            <radio value="{{item.value}}" checked="{{formData.status === item.value}}" />
            <text class="radio-text">{{item.text}}</text>
          </label>
        </radio-group>
      </view>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <button
          class="btn-submit btn-primary"
          formType="submit"
          disabled="{{submitLoading || !canSubmit}}"
        >
          {{submitLoading ? '保存中...' : (isEdit ? '更新人员' : '添加人员')}}
        </button>

        <button
          class="btn-cancel btn-secondary"
          bindtap="handleCancel"
          disabled="{{submitLoading}}"
        >
          取消
        </button>

        <button
          class="btn-delete btn-danger"
          bindtap="handleDelete"
          disabled="{{submitLoading}}"
          wx:if="{{isEdit}}"
        >
          删除人员
        </button>
      </view>
    </form>
  </view>
</view>

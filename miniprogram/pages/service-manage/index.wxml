<!--service-manage.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">{{isEdit ? '编辑服务' : '添加服务'}}</text>
  </view>

  <!-- 服务表单 -->
  <view class="form-container">
    <form bindsubmit="handleSubmit">
      <!-- 服务名称 -->
      <view class="form-group">
        <text class="form-label">服务名称 *</text>
        <input
          class="form-input"
          name="name"
          placeholder="请输入服务名称"
          value="{{formData.name}}"
          bindinput="onInputChange"
          data-field="name"
        />
      </view>

      <!-- 服务描述 -->
      <view class="form-group">
        <text class="form-label">服务描述 *</text>
        <textarea
          class="form-textarea"
          name="description"
          placeholder="请输入服务描述"
          value="{{formData.description}}"
          bindinput="onInputChange"
          data-field="description"
          maxlength="200"
        ></textarea>
      </view>

      <!-- 服务价格 -->
      <view class="form-group">
        <text class="form-label">服务价格 (元/小时) *</text>
        <input
          class="form-input"
          name="price"
          type="number"
          placeholder="请输入服务价格"
          value="{{formData.price}}"
          bindinput="onInputChange"
          data-field="price"
        />
      </view>

      <!-- 服务时长 -->
      <view class="form-group">
        <text class="form-label">服务时长 (小时) *</text>
        <input
          class="form-input"
          name="duration"
          type="number"
          placeholder="请输入服务时长"
          value="{{formData.duration}}"
          bindinput="onInputChange"
          data-field="duration"
        />
      </view>

      <!-- 服务分类 -->
      <view class="form-group">
        <text class="form-label">服务分类 *</text>
        <picker
          mode="selector"
          range="{{categories}}"
          range-key="name"
          value="{{categoryIndex}}"
          bindchange="onCategoryChange"
        >
          <view class="picker-view">
            <text class="picker-text">{{formData.categoryText || '请选择服务分类'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 服务标签 -->
      <view class="form-group">
        <text class="form-label">服务标签</text>
        <view class="tags-container">
          <view class="tag-item" wx:for="{{formData.tags}}" wx:key="index">
            <text class="tag-text">{{item}}</text>
            <text class="tag-remove" bindtap="removeTag" data-index="{{index}}">×</text>
          </view>
          <input
            class="tag-input"
            placeholder="输入标签后按回车"
            value="{{tagInput}}"
            bindinput="onTagInput"
            bindconfirm="addTag"
          />
        </view>
      </view>

      <!-- 服务状态 -->
      <view class="form-group">
        <text class="form-label">服务状态</text>
        <radio-group bindchange="onStatusChange">
          <label class="radio-item" wx:for="{{statusOptions}}" wx:key="value">
            <radio value="{{item.value}}" checked="{{formData.status === item.value}}" />
            <text class="radio-text">{{item.text}}</text>
          </label>
        </radio-group>
      </view>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <button
          class="btn-submit btn-primary"
          formType="submit"
          disabled="{{submitLoading || !canSubmit}}"
        >
          {{submitLoading ? '保存中...' : (isEdit ? '更新服务' : '添加服务')}}
        </button>

        <button
          class="btn-cancel btn-secondary"
          bindtap="handleCancel"
          disabled="{{submitLoading}}"
        >
          取消
        </button>

        <button
          class="btn-delete btn-danger"
          bindtap="handleDelete"
          disabled="{{submitLoading}}"
          wx:if="{{isEdit}}"
        >
          删除服务
        </button>
      </view>
    </form>
  </view>
</view>

<!--miniprogram/pages/deployService/index.wxml-->
<view class="page">
  <view class="title">功能介绍</view>
  <view class="info">云托管是全托管的容器服务，支持任何语言及框架运行，只需将已有业务代码打包上传，即可快速迁移。</view>
  <view class="title">如何体验</view>
  <view class="info">步骤一：切换按量付费，打开“云开发控制台>设置>环境设置”找到按量付费，点击切换。</view>
  <image class="img" src="../../images/deploy_step1.png"></image>
  <view class="info">步骤二：开通云托管，体验相关能力</view>
  <image class="img" src="../../images/deploy_step2.png"></image>
</view>

<!--worker-detail.wxml-->
<view class="container">
  <view class="worker-detail" wx:if="{{worker}}">
    <!-- 服务人员基本信息 -->
    <view class="worker-header card">
      <image class="worker-avatar" src="{{worker.avatar}}" mode="aspectFill"></image>
      <view class="worker-basic-info">
        <text class="worker-name">{{worker.name}}</text>
        <view class="worker-status">
          <text class="status-tag status-{{worker.status}}">{{worker.statusText}}</text>
        </view>
        <text class="worker-phone">{{worker.phone}}</text>
        <view class="worker-rating">
          <text class="rating-text">评分：{{worker.rating}}</text>
          <view class="rating-stars">
            <text class="star" wx:for="{{5}}" wx:key="*this" style="color: {{index < worker.rating ? '#FFD700' : '#DDD'}}">★</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 服务人员详细信息 -->
    <view class="worker-info-section card">
      <view class="section-title">基本信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">年龄</text>
          <text class="info-value">{{worker.age}}岁</text>
        </view>
        <view class="info-item">
          <text class="info-label">工作经验</text>
          <text class="info-value">{{worker.experience}}年</text>
        </view>
        <view class="info-item">
          <text class="info-label">服务评分</text>
          <text class="info-value">{{worker.rating}}分</text>
        </view>
        <view class="info-item">
          <text class="info-label">当前状态</text>
          <text class="info-value">{{worker.statusText}}</text>
        </view>
      </view>
    </view>

    <!-- 专业技能 -->
    <view class="skills-section card">
      <view class="section-title">专业技能</view>
      <view class="skills-list">
        <text class="skill-tag" wx:for="{{worker.skills}}" wx:key="*this">{{item}}</text>
      </view>
    </view>

    <!-- 个人介绍 -->
    <view class="description-section card">
      <view class="section-title">个人介绍</view>
      <text class="description-text">{{worker.description}}</text>
    </view>

    <!-- 位置信息 -->
    <view class="location-section card" wx:if="{{worker.location}}">
      <view class="section-title">位置信息</view>
      <view class="location-info">
        <view class="location-item">
          <text class="location-icon">📍</text>
          <text class="location-text">{{worker.location.address}}</text>
        </view>
        <button class="btn-map btn-secondary" bindtap="showLocation">查看位置</button>
      </view>
    </view>

    <!-- 服务记录 -->
    <view class="service-records card">
      <view class="section-title">服务记录</view>
      <view class="records-stats">
        <view class="stat-item">
          <text class="stat-number">{{serviceStats.totalOrders}}</text>
          <text class="stat-label">总订单</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{serviceStats.completedOrders}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{serviceStats.satisfactionRate}}%</text>
          <text class="stat-label">满意度</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- 普通用户操作 -->
      <block wx:if="{{!userInfo.isAdmin}}">
        <button class="btn-contact btn-secondary" bindtap="contactWorker">联系服务人员</button>
        <button class="btn-book btn-primary" bindtap="bookService">预约服务</button>
      </block>

      <!-- 管理员操作 -->
      <block wx:if="{{userInfo.isAdmin}}">
        <button class="btn-edit btn-secondary" bindtap="editWorker">编辑</button>
        <button class="btn-delete btn-danger" bindtap="deleteWorker">删除</button>
        <button class="btn-select btn-primary" wx:if="{{worker.status === 'available'}}" bindtap="assignOrder">选择</button>
      </block>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:else>
    <text class="loading-text">加载中...</text>
  </view>
</view>

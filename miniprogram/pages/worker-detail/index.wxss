/**worker-detail.wxss**/

.container {
  padding: 0 0 120rpx 0;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 服务人员头部信息 */
.worker-header {
  margin: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.worker-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 25rpx;
}

.worker-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.worker-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.worker-status {
  margin-bottom: 10rpx;
}

.worker-phone {
  font-size: 28rpx;
  color: #4A90E2;
  margin-bottom: 15rpx;
}

.worker-rating {
  display: flex;
  align-items: center;
}

.rating-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.rating-stars {
  display: flex;
}

.star {
  font-size: 24rpx;
  margin-right: 2rpx;
}

/* 信息区域 */
.worker-info-section,
.skills-section,
.description-section,
.location-section,
.service-records {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 信息网格 */
.info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.info-item {
  flex: 1;
  min-width: 140rpx;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  border: 1rpx solid #E9ECEF;
  text-align: center;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 技能标签 */
.skills-list {
  display: flex;
  flex-wrap: wrap;
}

.skill-tag {
  background-color: #4A90E2;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

/* 个人介绍 */
.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 位置信息 */
.location-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.location-item {
  display: flex;
  align-items: flex-start;
  padding: 15rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.location-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  margin-top: 2rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.btn-map {
  width: 100%;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #4A90E2;
  color: white;
  border-radius: 8rpx;
  text-align: center;
}

/* 服务记录统计 */
.records-stats {
  display: flex;
  gap: 15rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  padding: 25rpx 15rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border: 1rpx solid #E9ECEF;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  border-top: 1rpx solid #E9ECEF;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 15rpx;
}

.action-buttons button {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 26rpx;
  font-weight: bold;
  margin: 0 5rpx;
}

.btn-contact {
  background-color: #6C757D;
  color: white;
}

.btn-book {
  background-color: #28A745;
  color: white;
}

.btn-edit {
  background-color: #6C757D;
  color: white;
}

.btn-delete {
  background-color: #DC3545;
  color: white;
}

.btn-select {
  background-color: #28A745;
  color: white;
}

.btn-contact:active {
  background-color: #5A6268;
}

.btn-book:active {
  background-color: #218838;
}

.btn-edit:active {
  background-color: #5A6268;
}

.btn-delete:active {
  background-color: #C82333;
}

.btn-select:active {
  background-color: #218838;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

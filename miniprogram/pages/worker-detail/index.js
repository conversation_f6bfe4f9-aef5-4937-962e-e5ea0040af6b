// worker-detail.js
const DataManager = require('../../utils/data.js');

Page({
  data: {
    workerId: null,
    worker: null,
    userInfo: null,
    serviceStats: {
      totalOrders: 0,
      completedOrders: 0,
      satisfactionRate: 0
    }
  },

  onLoad(options) {
    console.log('服务人员详情页面参数:', options);

    if (options.id) {
      this.setData({
        workerId: options.id
      });
      this.checkUserLogin();
      this.loadWorkerDetail();
      this.loadServiceStats();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 检查用户登录状态
  checkUserLogin() {
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({
      userInfo: userInfo || {}
    });
  },

  // 加载服务人员详情
  loadWorkerDetail() {
    const workerId = parseInt(this.data.workerId);
    const worker = DataManager.getWorkerById(workerId);

    console.log('加载的服务人员详情:', worker);

    if (worker) {
      this.setData({
        worker: worker
      });
      wx.setNavigationBarTitle({
        title: `${worker.name} - 详情`
      });
    } else {
      wx.showToast({
        title: '服务人员不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载服务统计数据
  loadServiceStats() {
    const workerId = parseInt(this.data.workerId);

    // 模拟服务统计数据
    const stats = {
      1: { totalOrders: 156, completedOrders: 148, satisfactionRate: 98 },
      2: { totalOrders: 89, completedOrders: 87, satisfactionRate: 99 },
      3: { totalOrders: 234, completedOrders: 220, satisfactionRate: 96 },
      4: { totalOrders: 67, completedOrders: 62, satisfactionRate: 94 }
    };

    const serviceStats = stats[workerId] || { totalOrders: 0, completedOrders: 0, satisfactionRate: 0 };

    this.setData({
      serviceStats: serviceStats
    });
  },

  // 联系服务人员
  contactWorker() {
    const worker = this.data.worker;
    if (!worker || !worker.phone) {
      wx.showToast({
        title: '暂无联系方式',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['拨打电话', '发送短信'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 拨打电话
          wx.makePhoneCall({
            phoneNumber: worker.phone.replace(/\*/g, '1'), // 模拟真实号码
            fail: (error) => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        } else if (res.tapIndex === 1) {
          // 发送短信
          wx.showToast({
            title: '短信功能待开发',
            icon: 'none'
          });
        }
      }
    });
  },

  // 预约服务
  bookService() {
    const worker = this.data.worker;

    if (worker.status !== 'available') {
      wx.showToast({
        title: '该服务人员当前不可用',
        icon: 'none'
      });
      return;
    }

    // 跳转到服务选择页面
    wx.navigateTo({
      url: '/pages/services/index?selectMode=true'
    });
  },

  // 编辑服务人员信息
  editWorker() {
    const workerId = this.data.workerId;
    wx.navigateTo({
      url: `/pages/worker-manage/index?id=${workerId}`
    });
  },

  // 分配订单
  assignOrder() {
    const workerId = this.data.workerId;
    const worker = this.data.worker;

    if (worker.status !== 'available') {
      wx.showToast({
        title: '该服务人员当前不可用',
        icon: 'none'
      });
      return;
    }

    // 跳转到订单分配页面
    wx.navigateTo({
      url: `/pages/assign-order/index?workerId=${workerId}`
    });
  },

  // 查看位置
  showLocation() {
    const worker = this.data.worker;

    if (!worker.location) {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      });
      return;
    }

    // 使用微信内置地图查看位置
    wx.openLocation({
      latitude: worker.location.latitude,
      longitude: worker.location.longitude,
      name: worker.name,
      address: worker.location.address,
      scale: 15,
      success: () => {
        console.log('打开地图成功');
      },
      fail: (error) => {
        console.error('打开地图失败:', error);
        wx.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    });
  },

  goBack() {
    wx.navigateBack();
  }
});

const DatabaseManager = require('../../utils/database.js');

Page({
  data: {
    cloudStatus: false,
    dbStatus: false,
    initLoading: false,
    logs: []
  },

  onLoad() {
    this.checkCloudStatus();
  },

  // 检查云开发状态
  async checkCloudStatus() {
    try {
      // 检查云开发是否可用
      const result = await wx.cloud.callFunction({
        name: 'login'
      });
      
      this.setData({
        cloudStatus: true
      });
      
      this.addLog('云开发连接成功', 'success');
      this.checkDatabaseStatus();
    } catch (error) {
      console.error('云开发连接失败:', error);
      this.setData({
        cloudStatus: false
      });
      this.addLog('云开发连接失败，请检查配置', 'error');
    }
  },

  // 检查数据库状态
  async checkDatabaseStatus() {
    try {
      const db = wx.cloud.database();
      
      // 检查用户表是否存在数据
      const userResult = await db.collection('users').limit(1).get();
      
      if (userResult.data.length > 0) {
        this.setData({
          dbStatus: true
        });
        this.addLog('数据库已初始化', 'success');
      } else {
        this.setData({
          dbStatus: false
        });
        this.addLog('数据库未初始化', 'info');
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error);
      this.setData({
        dbStatus: false
      });
      this.addLog('检查数据库状态失败', 'error');
    }
  },

  // 检查状态
  checkStatus() {
    this.addLog('开始检查状态...', 'info');
    this.checkCloudStatus();
  },

  // 初始化数据库
  async initDatabase() {
    if (!this.data.cloudStatus) {
      wx.showToast({
        title: '云开发未连接',
        icon: 'none'
      });
      return;
    }

    this.setData({
      initLoading: true,
      logs: []
    });

    this.addLog('开始初始化数据库...', 'info');

    try {
      // 直接使用数据库操作，不依赖云函数
      const db = wx.cloud.database();

      // 初始化用户数据
      this.addLog('正在初始化用户数据...', 'info');
      await this.initUsersDirectly(db);

      // 初始化服务数据
      this.addLog('正在初始化服务数据...', 'info');
      await this.initServicesDirectly(db);

      // 初始化服务人员数据
      this.addLog('正在初始化服务人员数据...', 'info');
      await this.initWorkersDirectly(db);

      // 初始化订单数据
      this.addLog('正在初始化订单数据...', 'info');
      await this.initOrdersDirectly(db);

      this.setData({
        dbStatus: true,
        initLoading: false
      });
      this.addLog('数据库初始化成功！', 'success');

      wx.showModal({
        title: '初始化成功',
        content: '数据库初始化完成！\n\n请到云开发控制台查看数据库表：\n- users (用户表)\n- services (服务表)\n- workers (人员表)\n- orders (订单表)',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });

    } catch (error) {
      console.error('初始化数据库失败:', error);
      this.setData({
        initLoading: false
      });
      this.addLog(`初始化异常: ${error.message}`, 'error');

      wx.showModal({
        title: '初始化失败',
        content: `错误信息：${error.message}\n\n请检查：\n1. 云开发环境ID是否正确\n2. 网络连接是否正常\n3. 云开发权限是否配置`,
        showCancel: false
      });
    }
  },

  // 跳过初始化
  skipInit() {
    wx.showModal({
      title: '跳过初始化',
      content: '跳过初始化可能导致部分功能无法正常使用，确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          // 设置跳过标记
          wx.setStorageSync('skipDatabaseInit', true);
          
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 添加日志
  addLog(message, type = 'info') {
    const now = new Date();
    const time = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

    const logs = this.data.logs;
    logs.push({
      time: time,
      message: message,
      type: type
    });

    this.setData({
      logs: logs
    });

    console.log(`[${time}] ${message}`);
  },

  // 直接初始化用户数据
  async initUsersDirectly(db) {
    const users = [
      {
        phone: '13800000000',
        name: '管理员',
        password: '123456',
        isAdmin: true,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000001',
        name: '普通用户',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000002',
        name: '张女士',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000003',
        name: '李女士',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      },
      {
        phone: '13800000004',
        name: '陈先生',
        password: '123456',
        isAdmin: false,
        avatar: '/images/icons/avatar.png',
        createTime: new Date()
      }
    ];

    for (let user of users) {
      try {
        // 检查用户是否已存在
        const existUser = await db.collection('users').where({
          phone: user.phone
        }).get();

        if (existUser.data.length === 0) {
          await db.collection('users').add({
            data: user
          });
          this.addLog(`用户 ${user.name} 添加成功`, 'success');
        } else {
          this.addLog(`用户 ${user.name} 已存在`, 'info');
        }
      } catch (error) {
        this.addLog(`添加用户 ${user.name} 失败: ${error.message}`, 'error');
      }
    }
  },

  // 直接初始化服务数据
  async initServicesDirectly(db) {
    const services = [
      {
        name: '家庭保洁',
        description: '专业的家庭清洁服务，包括地面清洁、家具擦拭、厨房卫生间深度清洁等。我们使用环保清洁剂，确保家庭环境安全健康。',
        price: 80,
        duration: 3,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['深度清洁', '专业工具', '环保清洁剂'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '月嫂服务',
        description: '专业月嫂提供产妇护理、新生儿护理、营养配餐等全方位服务。月嫂持有相关资格证书，经验丰富。',
        price: 200,
        duration: 24,
        category: 'childcare',
        image: '/images/default-goods-image.png',
        tags: ['专业护理', '24小时', '营养配餐'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '老人陪护',
        description: '为老年人提供日常生活照料、健康监护、心理陪伴等服务。护理人员有爱心和耐心，能够给老人提供专业的护理和温暖的陪伴。',
        price: 150,
        duration: 8,
        category: 'eldercare',
        image: '/images/default-goods-image.png',
        tags: ['贴心照料', '经验丰富', '健康监护'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '家电维修',
        description: '专业维修各类家用电器，包括空调、洗衣机、冰箱、电视等。我们有专业的维修技师，使用原厂配件，提供质保服务。',
        price: 100,
        duration: 2,
        category: 'maintenance',
        image: '/images/default-goods-image.png',
        tags: ['专业技师', '原厂配件', '质保服务'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '钟点工服务',
        description: '灵活的钟点工服务，可根据您的需求安排工作时间和内容。服务人员经过培训，工作认真负责。',
        price: 50,
        duration: 2,
        category: 'cleaning',
        image: '/images/default-goods-image.png',
        tags: ['灵活安排', '多样服务', '按时计费'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      },
      {
        name: '育儿嫂服务',
        description: '专业育儿嫂提供婴幼儿护理、早教启蒙、营养配餐等服务。育儿嫂持有相关资格证书，经验丰富。',
        price: 180,
        duration: 8,
        category: 'childcare',
        image: '/images/default-goods-image.png',
        tags: ['专业护理', '早教启蒙', '营养配餐'],
        status: 'active',
        statusText: '可预约',
        createTime: new Date()
      }
    ];

    for (let service of services) {
      try {
        // 检查服务是否已存在
        const existService = await db.collection('services').where({
          name: service.name
        }).get();

        if (existService.data.length === 0) {
          await db.collection('services').add({
            data: service
          });
          this.addLog(`服务 ${service.name} 添加成功`, 'success');
        } else {
          this.addLog(`服务 ${service.name} 已存在`, 'info');
        }
      } catch (error) {
        this.addLog(`添加服务 ${service.name} 失败: ${error.message}`, 'error');
      }
    }
  },

  // 直接初始化服务人员数据
  async initWorkersDirectly(db) {
    const workers = [
      {
        name: '王阿姨',
        phone: '137****9876',
        age: 45,
        experience: 8,
        rating: 4.8,
        avatar: '/images/icons/avatar.png',
        skills: ['家庭保洁', '老人护理', '烹饪'],
        description: '有丰富的家政服务经验，工作认真负责，深受客户好评。擅长各种清洁技巧，对老人护理有专业知识。',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      },
      {
        name: '刘阿姨',
        phone: '135****2468',
        age: 38,
        experience: 5,
        rating: 4.9,
        avatar: '/images/icons/avatar.png',
        skills: ['月嫂服务', '育儿护理', '营养配餐'],
        description: '专业月嫂，持有相关资格证书，服务细致周到。在产妇护理和新生儿护理方面有丰富经验。',
        status: 'busy',
        statusText: '服务中',
        createTime: new Date()
      },
      {
        name: '张师傅',
        phone: '138****1357',
        age: 52,
        experience: 15,
        rating: 4.7,
        avatar: '/images/icons/avatar.png',
        skills: ['家电维修', '水电维修', '家具安装'],
        description: '专业维修师傅，技术过硬，解决各种家庭维修问题。有多年的维修经验，工具齐全。',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      },
      {
        name: '李阿姨',
        phone: '139****7531',
        age: 42,
        experience: 6,
        rating: 4.6,
        avatar: '/images/icons/avatar.png',
        skills: ['老人陪护', '康复护理', '心理疏导'],
        description: '专业护理人员，有爱心和耐心，擅长老人护理。具备基本的医护知识，能够处理突发情况。',
        status: 'unavailable',
        statusText: '休假中',
        createTime: new Date()
      },
      {
        name: '陈师傅',
        phone: '136****8642',
        age: 48,
        experience: 12,
        rating: 4.5,
        avatar: '/images/icons/avatar.png',
        skills: ['钟点工', '搬家服务', '家具组装'],
        description: '多年钟点工经验，服务项目广泛，工作效率高。能够胜任各种家庭服务工作。',
        status: 'available',
        statusText: '空闲中',
        createTime: new Date()
      }
    ];

    for (let worker of workers) {
      try {
        // 检查服务人员是否已存在
        const existWorker = await db.collection('workers').where({
          name: worker.name
        }).get();

        if (existWorker.data.length === 0) {
          await db.collection('workers').add({
            data: worker
          });
          this.addLog(`服务人员 ${worker.name} 添加成功`, 'success');
        } else {
          this.addLog(`服务人员 ${worker.name} 已存在`, 'info');
        }
      } catch (error) {
        this.addLog(`添加服务人员 ${worker.name} 失败: ${error.message}`, 'error');
      }
    }
  },

  // 直接初始化订单数据
  async initOrdersDirectly(db) {
    const orders = [
      {
        orderNumber: 'HZ202401150001',
        serviceName: '家庭保洁',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-16 09:00-12:00',
        address: '杭州市西湖区文三路123号',
        totalPrice: 240,
        duration: 3,
        status: 'pending',
        statusText: '待确认',
        customerName: '张女士',
        customerPhone: '138****5678',
        workerName: '',
        workerPhone: '',
        notes: '请提前电话联系，家里有小孩需要注意安全。',
        createTime: new Date('2024-01-15 10:30')
      },
      {
        orderNumber: 'HZ202401140002',
        serviceName: '月嫂服务',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-15 08:00-次日08:00',
        address: '杭州市拱墅区莫干山路456号',
        totalPrice: 4800,
        duration: 24,
        status: 'in_progress',
        statusText: '进行中',
        customerName: '李女士',
        customerPhone: '139****1234',
        workerName: '刘阿姨',
        workerPhone: '135****2468',
        notes: '产妇刚生产，需要专业护理，请准备相关用品。',
        createTime: new Date('2024-01-14 14:20')
      },
      {
        orderNumber: 'HZ202401130003',
        serviceName: '老人陪护',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-14 08:00-18:00',
        address: '杭州市江干区钱江路789号',
        totalPrice: 1200,
        duration: 8,
        status: 'completed',
        statusText: '已完成',
        customerName: '陈先生',
        customerPhone: '136****5432',
        workerName: '李阿姨',
        workerPhone: '139****7531',
        notes: '老人有高血压，需要定时测量血压，注意饮食。',
        createTime: new Date('2024-01-13 16:45')
      },
      {
        orderNumber: 'HZ202401120004',
        serviceName: '家电维修',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-13 14:00-16:00',
        address: '杭州市滨江区江南大道888号',
        totalPrice: 200,
        duration: 2,
        status: 'completed',
        statusText: '已完成',
        customerName: '王先生',
        customerPhone: '137****9999',
        workerName: '张师傅',
        workerPhone: '138****1357',
        notes: '空调制冷效果不好，需要检查制冷剂。',
        createTime: new Date('2024-01-12 09:15')
      },
      {
        orderNumber: 'HZ202401110005',
        serviceName: '钟点工服务',
        serviceImage: '/images/default-goods-image.png',
        serviceTime: '2024-01-12 10:00-12:00',
        address: '杭州市余杭区文一西路999号',
        totalPrice: 100,
        duration: 2,
        status: 'cancelled',
        statusText: '已取消',
        customerName: '赵女士',
        customerPhone: '135****7777',
        workerName: '',
        workerPhone: '',
        notes: '临时有事，需要取消服务。',
        createTime: new Date('2024-01-11 15:30')
      }
    ];

    for (let order of orders) {
      try {
        // 检查订单是否已存在
        const existOrder = await db.collection('orders').where({
          orderNumber: order.orderNumber
        }).get();

        if (existOrder.data.length === 0) {
          await db.collection('orders').add({
            data: order
          });
          this.addLog(`订单 ${order.orderNumber} 添加成功`, 'success');
        } else {
          this.addLog(`订单 ${order.orderNumber} 已存在`, 'info');
        }
      } catch (error) {
        this.addLog(`添加订单 ${order.orderNumber} 失败: ${error.message}`, 'error');
      }
    }
  }
});

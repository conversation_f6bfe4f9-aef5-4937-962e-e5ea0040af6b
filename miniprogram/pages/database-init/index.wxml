<!--database-init.wxml-->
<view class="container">
  <view class="header">
    <image class="icon" src="/images/icons/setting.svg"></image>
    <text class="title">数据库初始化</text>
    <text class="subtitle">首次使用需要初始化数据库</text>
  </view>

  <view class="content">
    <!-- 初始化状态 -->
    <view class="status-section">
      <view class="status-item">
        <text class="status-label">云开发状态：</text>
        <text class="status-value {{cloudStatus ? 'success' : 'error'}}">
          {{cloudStatus ? '已连接' : '未连接'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">数据库状态：</text>
        <text class="status-value {{dbStatus ? 'success' : 'error'}}">
          {{dbStatus ? '已初始化' : '未初始化'}}
        </text>
      </view>
    </view>

    <!-- 初始化说明 -->
    <view class="info-section">
      <view class="info-title">初始化内容</view>
      <view class="info-list">
        <view class="info-item">
          <image class="info-icon" src="/images/icons/usercenter.png"></image>
          <view class="info-content">
            <text class="info-name">用户数据</text>
            <text class="info-desc">管理员和普通用户账号</text>
          </view>
        </view>
        <view class="info-item">
          <image class="info-icon" src="/images/icons/business.png"></image>
          <view class="info-content">
            <text class="info-name">服务数据</text>
            <text class="info-desc">6个家政服务项目</text>
          </view>
        </view>
        <view class="info-item">
          <image class="info-icon" src="/images/icons/avatar.png"></image>
          <view class="info-content">
            <text class="info-name">人员数据</text>
            <text class="info-desc">4个服务人员信息</text>
          </view>
        </view>
        <view class="info-item">
          <image class="info-icon" src="/images/icons/goods.png"></image>
          <view class="info-content">
            <text class="info-name">订单数据</text>
            <text class="info-desc">3个示例订单</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        class="btn-init btn-primary" 
        bindtap="initDatabase" 
        disabled="{{initLoading || !cloudStatus}}"
      >
        {{initLoading ? '初始化中...' : '开始初始化'}}
      </button>
      
      <button 
        class="btn-check btn-secondary" 
        bindtap="checkStatus"
        disabled="{{initLoading}}"
      >
        检查状态
      </button>
      
      <button 
        class="btn-skip btn-outline" 
        bindtap="skipInit"
        disabled="{{initLoading}}"
      >
        跳过初始化
      </button>
    </view>

    <!-- 初始化日志 -->
    <view class="log-section" wx:if="{{logs.length > 0}}">
      <view class="log-title">初始化日志</view>
      <scroll-view class="log-content" scroll-y="true">
        <view class="log-item" wx:for="{{logs}}" wx:key="index">
          <text class="log-time">{{item.time}}</text>
          <text class="log-message {{item.type}}">{{item.message}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <view class="help-title">使用说明</view>
    <view class="help-content">
      <text class="help-text">1. 确保已开通微信云开发服务</text>
      <text class="help-text">2. 在app.js中配置正确的环境ID</text>
      <text class="help-text">3. 点击"开始初始化"创建数据库表和数据</text>
      <text class="help-text">4. 初始化完成后即可正常使用小程序</text>
    </view>
  </view>
</view>

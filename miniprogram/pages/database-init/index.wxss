/**database-init.wxss**/

.container {
  padding: 40rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 内容区域 */
.content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 状态区域 */
.status-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 28rpx;
  color: #333;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-value.success {
  color: #28A745;
}

.status-value.error {
  color: #DC3545;
}

/* 信息区域 */
.info-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.info-desc {
  font-size: 24rpx;
  color: #666;
}

/* 操作区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn-init,
.btn-check,
.btn-skip {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 10rpx;
}

.btn-primary {
  background-color: #4A90E2;
  color: white;
}

.btn-secondary {
  background-color: #6C757D;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #4A90E2;
  border: 2rpx solid #4A90E2;
}

.btn-init[disabled],
.btn-check[disabled],
.btn-skip[disabled] {
  background-color: #CED4DA;
  color: #6C757D;
  border-color: #CED4DA;
}

/* 日志区域 */
.log-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.log-content {
  max-height: 400rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.log-time {
  color: #666;
  margin-right: 15rpx;
  min-width: 120rpx;
}

.log-message {
  flex: 1;
}

.log-message.success {
  color: #28A745;
}

.log-message.error {
  color: #DC3545;
}

.log-message.info {
  color: #17A2B8;
}

/* 帮助区域 */
.help-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-top: 20rpx;
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.help-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

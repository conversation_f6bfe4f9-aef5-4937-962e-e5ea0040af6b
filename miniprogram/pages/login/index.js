const API = require('../../utils/api.js');

Page({
  data: {
    loginType: 'wechat', // 'wechat' 或 'admin'
    phone: '',
    password: '',
    loginLoading: false,
    canLogin: false,
    canWechatLogin: false,
    userInfo: {
      avatarUrl: '/images/icons/avatar.png',
      nickName: ''
    }
  },

  onLoad() {
    // 检查是否已登录
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 切换登录方式
  switchLoginType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      loginType: type,
      phone: '',
      password: '',
      canLogin: false,
      canWechatLogin: false
    });
  },

  // 选择头像
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    console.log('选择头像:', avatarUrl);

    this.setData({
      'userInfo.avatarUrl': avatarUrl
    });

    this.checkWechatLogin();
  },

  // 输入昵称
  onNicknameInput(e) {
    const nickName = e.detail.value;
    console.log('输入昵称:', nickName);

    this.setData({
      'userInfo.nickName': nickName
    });

    this.checkWechatLogin();
  },

  // 检查微信登录条件
  checkWechatLogin() {
    const { userInfo } = this.data;
    const canWechatLogin = userInfo.avatarUrl &&
                          userInfo.avatarUrl !== '/images/icons/avatar.png' &&
                          userInfo.nickName &&
                          userInfo.nickName.trim().length > 0;

    this.setData({
      canWechatLogin: canWechatLogin
    });
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({
      phone: phone
    });
    this.checkCanLogin();
  },

  // 密码输入
  onPasswordInput(e) {
    const password = e.detail.value;
    this.setData({
      password: password
    });
    this.checkCanLogin();
  },

  // 微信登录
  async handleWechatLogin() {
    if (this.data.loginLoading || !this.data.canWechatLogin) return;

    this.setData({
      loginLoading: true
    });

    try {
      const { userInfo } = this.data;

      // 获取微信登录凭证
      const loginRes = await this.wxLogin();

      // 创建用户信息
      const finalUserInfo = {
        id: Date.now(), // 使用时间戳作为临时ID
        phone: '', // 微信登录不需要手机号
        name: userInfo.nickName,
        isAdmin: false, // 微信登录默认为普通用户
        avatar: userInfo.avatarUrl,
        openid: loginRes.code || 'temp_' + Date.now() // 使用登录凭证或临时ID
      };

      console.log('创建的用户信息:', finalUserInfo);

      // 保存用户信息
      wx.setStorageSync('userInfo', finalUserInfo);

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loginLoading: false
      });
    }
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          console.log('微信登录成功:', res);
          resolve(res);
        },
        fail: (error) => {
          console.error('微信登录失败:', error);
          reject(error);
        }
      });
    });
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { phone, password } = this.data;
    const canLogin = phone.length === 11 && password.length >= 6;
    this.setData({
      canLogin: canLogin
    });
  },

  // 管理员登录
  async handleAdminLogin() {
    if (!this.data.canLogin || this.data.loginLoading) return;

    const { phone, password } = this.data;

    this.setData({
      loginLoading: true
    });

    try {
      // 首先尝试数据库登录
      const result = await API.user.login(phone, password);

      this.setData({
        loginLoading: false
      });

      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        // 数据库登录失败，尝试模拟登录（兼容模式）
        this.fallbackAdminLogin(phone, password);
      }
    } catch (error) {
      console.error('数据库登录失败，使用模拟登录:', error);
      this.setData({
        loginLoading: false
      });
      // 数据库连接失败，使用模拟登录
      this.fallbackAdminLogin(phone, password);
    }
  },

  // 管理员模拟登录（兼容模式）
  fallbackAdminLogin(phone, password) {
    this.setData({
      loginLoading: true
    });

    setTimeout(() => {
      let loginSuccess = false;
      let userInfo = null;

      // 管理员账号验证
      if (phone === '13800000000' && password === '123456') {
        // 管理员登录
        loginSuccess = true;
        userInfo = {
          id: 1,
          phone: phone,
          name: '管理员',
          isAdmin: true,
          avatar: '/images/icons/avatar.png'
        };
      } else if (phone.length === 11 && password === '123456') {
        // 其他手机号也可以作为管理员登录
        loginSuccess = true;
        userInfo = {
          id: parseInt(phone.slice(-4)), // 用手机号后4位作为ID
          phone: phone,
          name: '管理员',
          isAdmin: true,
          avatar: '/images/icons/avatar.png'
        };
      }

      this.setData({
        loginLoading: false
      });

      if (loginSuccess) {
        // 保存用户信息
        wx.setStorageSync('userInfo', userInfo);

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: '账号或密码错误',
          icon: 'none'
        });
      }
    }, 1000);
  },

  // 使用演示账号
  useDemo(e) {
    const type = e.currentTarget.dataset.type;

    if (type === 'admin') {
      this.setData({
        phone: '13800000000',
        password: '123456',
        canLogin: true
      });
    }
  },


});

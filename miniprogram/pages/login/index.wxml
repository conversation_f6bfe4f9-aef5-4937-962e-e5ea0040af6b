<!--login.wxml-->
<view class="page">
  <!-- 头部信息 -->
  <view class="header">
    <view class="logo-box">
      <image class="logo" src="/images/icons/avatar.png"></image>
    </view>
    <view class="title">家政服务管理</view>
    <view class="subtitle">专业的家政服务管理平台</view>
  </view>

  <!-- 登录方式选择 -->
  <view class="login-tabs">
    <view class="tab-item {{loginType === 'wechat' ? 'active' : ''}}" bindtap="switchLoginType" data-type="wechat">
      <text>微信登录</text>
    </view>
    <view class="tab-item {{loginType === 'admin' ? 'active' : ''}}" bindtap="switchLoginType" data-type="admin">
      <text>管理员登录</text>
    </view>
  </view>

  <!-- 微信登录 -->
  <view class="form" wx:if="{{loginType === 'wechat'}}">
    <view class="wechat-login">
      <!-- 头像选择 -->
      <view class="avatar-section">
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image class="wechat-avatar" src="{{userInfo.avatarUrl || '/images/icons/avatar.png'}}"></image>
          <view class="avatar-tip">点击选择头像</view>
        </button>
      </view>

      <!-- 昵称输入 -->
      <view class="nickname-section">
        <input
          class="nickname-input"
          type="nickname"
          placeholder="请输入昵称"
          value="{{userInfo.nickName}}"
          bindinput="onNicknameInput"
          maxlength="20"
        />
      </view>

      <!-- 登录按钮 -->
      <button class="wechat-login-btn" bindtap="handleWechatLogin" disabled="{{loginLoading || !canWechatLogin}}">
        <image class="wechat-icon" src="/images/icons/avatar.png"></image>
        {{loginLoading ? '登录中...' : '微信登录'}}
      </button>

      <view class="wechat-tips">
        <text>请选择头像并输入昵称完成登录</text>
      </view>
    </view>
  </view>

  <!-- 管理员登录 -->
  <view class="form" wx:if="{{loginType === 'admin'}}">
    <view class="input-group">
      <text class="label">手机号</text>
      <input class="input" type="number" placeholder="请输入手机号" value="{{phone}}" bindinput="onPhoneInput" maxlength="11" />
    </view>

    <view class="input-group">
      <text class="label">密码</text>
      <input class="input" type="password" placeholder="请输入密码" value="{{password}}" bindinput="onPasswordInput" />
    </view>

    <button class="login-btn" bindtap="handleAdminLogin" disabled="{{!canLogin}}">
      {{loginLoading ? '登录中...' : '管理员登录'}}
    </button>
  </view>

  <!-- 演示账号 -->
  <view class="demo" wx:if="{{loginType === 'admin'}}">
    <view class="demo-title">演示账号</view>
    <view class="demo-item" bindtap="useDemo" data-type="admin">
      <text class="demo-label">管理员：</text>
      <text class="demo-account">*********** / 123456</text>
    </view>
  </view>
</view>

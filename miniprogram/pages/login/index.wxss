/**login.wxss**/

/* 页面样式 */
page {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  height: 100vh;
}

.page {
  min-height: 100vh;
  padding: 80rpx 60rpx 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo-box {
  margin-bottom: 30rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录方式选择 */
.login-tabs {
  display: flex;
  margin: 60rpx 40rpx 40rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: white;
  color: #4A90E2;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.form {
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 10rpx;
  font-size: 32rpx;
  background-color: #F8F9FA;
  box-sizing: border-box;
  color: #333;
  line-height: 80rpx;
  display: flex;
  align-items: center;
}

/* 复选框样式 */
.checkbox-group {
  margin-bottom: 30rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-text {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #666;
}

/* 微信登录样式 */
.wechat-login {
  text-align: center;
}

/* 头像选择 */
.avatar-section {
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  background: none;
  border: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-wrapper::after {
  border: none;
}

.wechat-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #E9ECEF;
  margin-bottom: 15rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666;
}

/* 昵称输入 */
.nickname-section {
  margin-bottom: 40rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #E9ECEF;
  border-radius: 10rpx;
  font-size: 32rpx;
  background-color: #F8F9FA;
  box-sizing: border-box;
  color: #333;
  text-align: center;
}

/* 微信提示 */
.wechat-tips {
  margin-top: 20rpx;
}

.wechat-tips text {
  font-size: 24rpx;
  color: #666;
}

.wechat-login-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  background-color: #07C160;
  color: white;
  border: none;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.wechat-login-btn[disabled] {
  background-color: #CED4DA;
  color: #6C757D;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

/* 按钮样式 */
.login-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.login-btn[disabled] {
  background-color: #CED4DA;
  color: #6C757D;
}

/* 提示样式 */
.tips {
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
}

.link-text {
  font-size: 26rpx;
  color: #4A90E2;
  margin-left: 10rpx;
}

/* 演示账号样式 */
.demo {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
}

.demo-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #E9ECEF;
}

.demo-item:last-child {
  border-bottom: none;
}

.demo-label {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
}

.demo-account {
  font-size: 26rpx;
  color: #4A90E2;
  font-family: monospace;
}
